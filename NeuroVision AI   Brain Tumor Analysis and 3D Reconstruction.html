<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroVision AI | Brain Tumor Analysis & 3D Reconstruction</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #0ea5e9 100%);
        }
        .scan-upload-area {
            border: 2px dashed #93c5fd;
            transition: all 0.3s ease;
        }
        .scan-upload-area:hover {
            border-color: #3b82f6;
            background-color: rgba(147, 197, 253, 0.1);
        }
        .tumor-marker {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }
        .slice-control {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #d1d5db;
            outline: none;
        }
        .slice-control::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
        }
        #viewer-container {
            position: relative;
            overflow: hidden;
        }
        .loading-spinner {
            border-top-color: #3b82f6;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Navigation -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-brain text-2xl"></i>
                <span class="text-xl font-bold">NeuroVision AI</span>
            </div>
            <div class="hidden md:flex space-x-6">
                <a href="#" class="hover:text-blue-200 transition">Home</a>
                <a href="#" class="hover:text-blue-200 transition">Features</a>
                <a href="#" class="hover:text-blue-200 transition">Research</a>
                <a href="#" class="hover:text-blue-200 transition">About</a>
                <a href="#" class="hover:text-blue-200 transition">Contact</a>
            </div>
            <button class="md:hidden text-xl">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-16">
        <div class="container mx-auto px-4 flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-8 md:mb-0">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">Advanced Brain Tumor Analysis</h1>
                <p class="text-xl mb-6 text-blue-100">AI-powered MRI analysis with automated tumor detection and 3D reconstruction for precise diagnosis and treatment planning.</p>
                <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                    <button class="bg-white text-blue-800 font-semibold px-6 py-3 rounded-lg hover:bg-blue-100 transition">
                        <i class="fas fa-upload mr-2"></i> Upload MRI Scan
                    </button>
                    <button class="border border-white text-white font-semibold px-6 py-3 rounded-lg hover:bg-blue-700 transition">
                        <i class="fas fa-play mr-2"></i> See Demo
                    </button>
                </div>
            </div>
            <div class="md:w-1/2 flex justify-center">
                <div class="relative w-full max-w-md">
                    <div class="absolute -top-6 -left-6 w-32 h-32 bg-blue-400 rounded-full opacity-20"></div>
                    <div class="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-400 rounded-full opacity-20"></div>
                    <div class="relative bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl p-4 border border-blue-300 border-opacity-30">
                        <div class="bg-gray-800 rounded-lg overflow-hidden">
                            <img src="https://via.placeholder.com/500x300/1e40af/ffffff?text=MRI+Scan+Preview" alt="MRI Scan Preview" class="w-full h-auto">
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-red-500 rounded-full tumor-marker opacity-0" id="demo-tumor-marker"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12">
        <!-- Upload Section -->
        <section class="mb-16 bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="p-6 md:p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Upload Your MRI Scans</h2>
                <div class="flex flex-col md:flex-row gap-8">
                    <div class="md:w-1/2">
                        <div class="scan-upload-area rounded-lg p-8 text-center cursor-pointer mb-4" id="upload-area">
                            <i class="fas fa-cloud-upload-alt text-4xl text-blue-500 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-700 mb-2">Drag & Drop MRI Files</h3>
                            <p class="text-gray-500 mb-4">or click to browse files</p>
                            <p class="text-sm text-gray-400">Supports: DICOM, NIfTI, Analyze formats</p>
                            <input type="file" id="file-input" class="hidden" accept=".dcm,.nii,.hdr" multiple>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-700 mb-2">Upload Requirements</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i> Minimum 20 slices for 3D reconstruction</li>
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i> Axial view recommended</li>
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i> 1mm slice thickness preferred</li>
                            </ul>
                        </div>
                    </div>
                    <div class="md:w-1/2">
                        <div class="bg-gray-50 rounded-lg p-6 h-full">
                            <h3 class="font-semibold text-gray-700 mb-4">Recent Analyses</h3>
                            <div class="space-y-4" id="recent-analyses">
                                <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                        <i class="fas fa-file-medical text-blue-600"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">No recent analyses</p>
                                        <p class="text-sm text-gray-500">Upload new scans to begin</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <h4 class="font-medium text-gray-700 mb-2">Quick Start Guide</h4>
                                <ol class="text-sm text-gray-600 space-y-2">
                                    <li>1. Upload your MRI scan series</li>
                                    <li>2. Our AI will automatically analyze the images</li>
                                    <li>3. Review the tumor segmentation results</li>
                                    <li>4. Explore the 3D reconstruction</li>
                                    <li>5. Download the comprehensive report</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analysis Results -->
        <section class="mb-16" id="analysis-section" style="display: none;">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-6 md:p-8">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Analysis Results</h2>
                        <div class="flex space-x-3">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition flex items-center">
                                <i class="fas fa-download mr-2"></i> Export Report
                            </button>
                            <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition flex items-center">
                                <i class="fas fa-share-alt mr-2"></i> Share
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <div class="bg-blue-50 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-tag text-blue-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800">Tumor Classification</h3>
                            </div>
                            <p class="text-2xl font-bold text-gray-800 mb-2" id="tumor-type">-</p>
                            <p class="text-sm text-gray-600" id="tumor-confidence">Confidence: -</p>
                        </div>
                        <div class="bg-green-50 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-ruler-combined text-green-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800">Tumor Volume</h3>
                            </div>
                            <p class="text-2xl font-bold text-gray-800 mb-2" id="tumor-volume">-</p>
                            <p class="text-sm text-gray-600">Estimated from 3D reconstruction</p>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-map-marker-alt text-purple-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800">Tumor Location</h3>
                            </div>
                            <p class="text-2xl font-bold text-gray-800 mb-2" id="tumor-location">-</p>
                            <p class="text-sm text-gray-600" id="tumor-lobe">Brain lobe: -</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="font-semibold text-gray-800">2D Slice Viewer</h3>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span id="current-slice">1</span>/<span id="total-slices">-</span>
                                </div>
                            </div>
                            <div class="relative bg-gray-100 rounded-lg overflow-hidden mb-4" style="height: 300px;">
                                <div class="absolute inset-0 flex items-center justify-center" id="loading-spinner" style="display: none;">
                                    <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full loading-spinner"></div>
                                </div>
                                <canvas id="slice-viewer" class="w-full h-full"></canvas>
                                <div class="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded" id="tumor-indicator" style="display: none;">
                                    Tumor Detected
                                </div>
                            </div>
                            <input type="range" min="1" max="100" value="1" class="slice-control mb-2" id="slice-control">
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>Inferior</span>
                                <span>Superior</span>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-4">Tumor Characteristics</h3>
                            <div class="mb-6">
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Edema Presence</span>
                                    <span class="text-sm font-medium text-gray-700" id="edema-score">-</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-yellow-500 h-2.5 rounded-full" id="edema-bar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="mb-6">
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Contrast Enhancement</span>
                                    <span class="text-sm font-medium text-gray-700" id="enhancement-score">-</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-500 h-2.5 rounded-full" id="enhancement-bar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="mb-6">
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Necrosis Likelihood</span>
                                    <span class="text-sm font-medium text-gray-700" id="necrosis-score">-</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-red-500 h-2.5 rounded-full" id="necrosis-bar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-700 mb-2">Key Metrics</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-500">Tumor Longest Diameter</p>
                                        <p class="font-medium" id="tumor-diameter">- mm</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Distance to Ventricles</p>
                                        <p class="font-medium" id="ventricle-distance">- mm</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Midline Shift</p>
                                        <p class="font-medium" id="midline-shift">- mm</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">ADC Mean Value</p>
                                        <p class="font-medium" id="adc-value">- mm²/s</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-4">Tumor Growth Analysis</h3>
                            <canvas id="growth-chart" height="200"></canvas>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-4">Tissue Composition</h3>
                            <canvas id="composition-chart" height="200"></canvas>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-4 mb-8">
                        <h3 class="font-semibold text-gray-800 mb-4">3D Tumor Reconstruction</h3>
                        <div class="relative bg-gray-900 rounded-lg overflow-hidden" style="height: 400px;" id="viewer-container">
                            <div class="absolute inset-0 flex items-center justify-center" id="3d-loading">
                                <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full loading-spinner"></div>
                            </div>
                            <canvas id="3d-viewer" class="w-full h-full"></canvas>
                            <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-3">
                                <button class="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition" id="rotate-ccw">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition" id="rotate-cw">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition" id="zoom-in">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition" id="zoom-out">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition" id="reset-view">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Clinical Interpretation</h3>
                        <div class="prose max-w-none text-gray-700" id="clinical-notes">
                            <p>No clinical interpretation available. Please upload MRI scans for analysis.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">Advanced Neuroimaging Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="p-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-robot text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg text-gray-800 mb-2">Deep Learning Analysis</h3>
                        <p class="text-gray-600">Our proprietary neural networks provide accurate tumor segmentation and classification with 98.7% accuracy.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="p-6">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-cube text-green-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg text-gray-800 mb-2">Volumetric 3D Modeling</h3>
                        <p class="text-gray-600">Interactive 3D reconstruction helps visualize tumor morphology and spatial relationships with brain structures.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="p-6">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg text-gray-800 mb-2">Longitudinal Tracking</h3>
                        <p class="text-gray-600">Compare scans over time to monitor tumor progression and treatment response with quantitative metrics.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="p-6">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-dna text-yellow-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg text-gray-800 mb-2">Radiogenomic Correlation</h3>
                        <p class="text-gray-600">Advanced algorithms correlate imaging features with molecular markers for precision medicine approaches.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="p-6">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-procedures text-red-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg text-gray-800 mb-2">Surgical Planning</h3>
                        <p class="text-gray-600">Generate virtual resection plans with safety margins and critical structure avoidance for neurosurgical guidance.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="p-6">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-file-medical-alt text-indigo-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg text-gray-800 mb-2">Comprehensive Reporting</h3>
                        <p class="text-gray-600">Automated generation of standardized reports with quantitative measurements and visualizations for clinical use.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">NeuroVision AI</h3>
                    <p class="text-gray-400">Advanced neuroimaging analysis platform for precise brain tumor diagnosis and treatment planning.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Home</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Features</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Research</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Pricing</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Documentation</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">API Reference</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Case Studies</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Support</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center"><i class="fas fa-envelope mr-2 text-gray-400"></i> <span class="text-gray-400"><EMAIL></span></li>
                        <li class="flex items-center"><i class="fas fa-phone mr-2 text-gray-400"></i> <span class="text-gray-400">+****************</span></li>
                        <li class="flex items-center"><i class="fas fa-map-marker-alt mr-2 text-gray-400"></i> <span class="text-gray-400">San Francisco, CA</span></li>
                    </ul>
                </div>
            </div>
            <div class="pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 mb-4 md:mb-0">© 2023 NeuroVision AI. All rights reserved.</p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-github"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Demo animation
        document.addEventListener('DOMContentLoaded', function() {
            // Show demo tumor marker
            setTimeout(() => {
                document.getElementById('demo-tumor-marker').style.opacity = '0.7';
            }, 1500);

            // Upload area interaction
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');

            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-500', 'bg-blue-50');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
                if (e.dataTransfer.files.length > 0) {
                    simulateAnalysis(e.dataTransfer.files);
                }
            });

            fileInput.addEventListener('change', () => {
                if (fileInput.files.length > 0) {
                    simulateAnalysis(fileInput.files);
                }
            });

            // Slice control for demo purposes
            const sliceControl = document.getElementById('slice-control');
            sliceControl.addEventListener('input', () => {
                document.getElementById('current-slice').textContent = sliceControl.value;
                // Randomly show tumor indicator
                if (Math.random() > 0.7) {
                    document.getElementById('tumor-indicator').style.display = 'block';
                } else {
                    document.getElementById('tumor-indicator').style.display = 'none';
                }
            });

            // Initialize empty charts
            initCharts();
            
            // Initialize 3D viewer
            init3DViewer();
        });

        function simulateAnalysis(files) {
            // Show loading state
            document.getElementById('loading-spinner').style.display = 'flex';
            document.getElementById('analysis-section').style.display = 'none';
            
            // Simulate processing delay
            setTimeout(() => {
                // Hide loading spinner
                document.getElementById('loading-spinner').style.display = 'none';
                
                // Show analysis section
                document.getElementById('analysis-section').style.display = 'block';
                
                // Update analysis results with sample data
                document.getElementById('tumor-type').textContent = 'Glioblastoma (WHO Grade IV)';
                document.getElementById('tumor-confidence').textContent = 'Confidence: 96.5%';
                document.getElementById('tumor-volume').textContent = '42.7 cm³';
                document.getElementById('tumor-location').textContent = 'Right Temporal Lobe';
                document.getElementById('tumor-lobe').textContent = 'Brain lobe: Temporal';
                
                document.getElementById('edema-score').textContent = '87%';
                document.getElementById('edema-bar').style.width = '87%';
                
                document.getElementById('enhancement-score').textContent = '92%';
                document.getElementById('enhancement-bar').style.width = '92%';
                
                document.getElementById('necrosis-score').textContent = '78%';
                document.getElementById('necrosis-bar').style.width = '78%';
                
                document.getElementById('tumor-diameter').textContent = '48.2 mm';
                document.getElementById('ventricle-distance').textContent = '12.4 mm';
                document.getElementById('midline-shift').textContent = '3.7 mm';
                document.getElementById('adc-value').textContent = '0.89 × 10⁻³ mm²/s';
                
                document.getElementById('total-slices').textContent = '128';
                document.getElementById('slice-control').max = '128';
                
                // Update charts
                updateCharts();
                
                // Update clinical notes
                document.getElementById('clinical-notes').innerHTML = `
                    <h4>Key Findings</h4>
                    <ul>
                        <li>Large heterogeneously enhancing mass in the right temporal lobe with central necrosis</li>
                        <
</body>
</html>