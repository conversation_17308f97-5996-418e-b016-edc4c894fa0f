(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();var ro={exports:{}},al={},lo={exports:{}},T={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jn=Symbol.for("react.element"),jc=Symbol.for("react.portal"),Nc=Symbol.for("react.fragment"),wc=Symbol.for("react.strict_mode"),kc=Symbol.for("react.profiler"),Sc=Symbol.for("react.provider"),Ec=Symbol.for("react.context"),Cc=Symbol.for("react.forward_ref"),_c=Symbol.for("react.suspense"),Pc=Symbol.for("react.memo"),Mc=Symbol.for("react.lazy"),Wi=Symbol.iterator;function Tc(e){return e===null||typeof e!="object"?null:(e=Wi&&e[Wi]||e["@@iterator"],typeof e=="function"?e:null)}var so={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},io=Object.assign,ao={};function un(e,t,n){this.props=e,this.context=t,this.refs=ao,this.updater=n||so}un.prototype.isReactComponent={};un.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};un.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function oo(){}oo.prototype=un.prototype;function Qs(e,t,n){this.props=e,this.context=t,this.refs=ao,this.updater=n||so}var Ks=Qs.prototype=new oo;Ks.constructor=Qs;io(Ks,un.prototype);Ks.isPureReactComponent=!0;var Qi=Array.isArray,uo=Object.prototype.hasOwnProperty,Ys={current:null},co={key:!0,ref:!0,__self:!0,__source:!0};function fo(e,t,n){var r,l={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)uo.call(t,r)&&!co.hasOwnProperty(r)&&(l[r]=t[r]);var o=arguments.length-2;if(o===1)l.children=n;else if(1<o){for(var u=Array(o),d=0;d<o;d++)u[d]=arguments[d+2];l.children=u}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)l[r]===void 0&&(l[r]=o[r]);return{$$typeof:Jn,type:e,key:i,ref:a,props:l,_owner:Ys.current}}function zc(e,t){return{$$typeof:Jn,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Gs(e){return typeof e=="object"&&e!==null&&e.$$typeof===Jn}function Rc(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ki=/\/+/g;function Cl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Rc(""+e.key):t.toString(36)}function kr(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Jn:case jc:a=!0}}if(a)return a=e,l=l(a),e=r===""?"."+Cl(a,0):r,Qi(l)?(n="",e!=null&&(n=e.replace(Ki,"$&/")+"/"),kr(l,t,n,"",function(d){return d})):l!=null&&(Gs(l)&&(l=zc(l,n+(!l.key||a&&a.key===l.key?"":(""+l.key).replace(Ki,"$&/")+"/")+e)),t.push(l)),1;if(a=0,r=r===""?".":r+":",Qi(e))for(var o=0;o<e.length;o++){i=e[o];var u=r+Cl(i,o);a+=kr(i,t,n,u,l)}else if(u=Tc(e),typeof u=="function")for(e=u.call(e),o=0;!(i=e.next()).done;)i=i.value,u=r+Cl(i,o++),a+=kr(i,t,n,u,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function ir(e,t,n){if(e==null)return e;var r=[],l=0;return kr(e,r,"","",function(i){return t.call(n,i,l++)}),r}function Ic(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ue={current:null},Sr={transition:null},Lc={ReactCurrentDispatcher:ue,ReactCurrentBatchConfig:Sr,ReactCurrentOwner:Ys};function mo(){throw Error("act(...) is not supported in production builds of React.")}T.Children={map:ir,forEach:function(e,t,n){ir(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ir(e,function(){t++}),t},toArray:function(e){return ir(e,function(t){return t})||[]},only:function(e){if(!Gs(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};T.Component=un;T.Fragment=Nc;T.Profiler=kc;T.PureComponent=Qs;T.StrictMode=wc;T.Suspense=_c;T.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Lc;T.act=mo;T.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=io({},e.props),l=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=Ys.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(u in t)uo.call(t,u)&&!co.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&o!==void 0?o[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){o=Array(u);for(var d=0;d<u;d++)o[d]=arguments[d+2];r.children=o}return{$$typeof:Jn,type:e.type,key:l,ref:i,props:r,_owner:a}};T.createContext=function(e){return e={$$typeof:Ec,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Sc,_context:e},e.Consumer=e};T.createElement=fo;T.createFactory=function(e){var t=fo.bind(null,e);return t.type=e,t};T.createRef=function(){return{current:null}};T.forwardRef=function(e){return{$$typeof:Cc,render:e}};T.isValidElement=Gs;T.lazy=function(e){return{$$typeof:Mc,_payload:{_status:-1,_result:e},_init:Ic}};T.memo=function(e,t){return{$$typeof:Pc,type:e,compare:t===void 0?null:t}};T.startTransition=function(e){var t=Sr.transition;Sr.transition={};try{e()}finally{Sr.transition=t}};T.unstable_act=mo;T.useCallback=function(e,t){return ue.current.useCallback(e,t)};T.useContext=function(e){return ue.current.useContext(e)};T.useDebugValue=function(){};T.useDeferredValue=function(e){return ue.current.useDeferredValue(e)};T.useEffect=function(e,t){return ue.current.useEffect(e,t)};T.useId=function(){return ue.current.useId()};T.useImperativeHandle=function(e,t,n){return ue.current.useImperativeHandle(e,t,n)};T.useInsertionEffect=function(e,t){return ue.current.useInsertionEffect(e,t)};T.useLayoutEffect=function(e,t){return ue.current.useLayoutEffect(e,t)};T.useMemo=function(e,t){return ue.current.useMemo(e,t)};T.useReducer=function(e,t,n){return ue.current.useReducer(e,t,n)};T.useRef=function(e){return ue.current.useRef(e)};T.useState=function(e){return ue.current.useState(e)};T.useSyncExternalStore=function(e,t,n){return ue.current.useSyncExternalStore(e,t,n)};T.useTransition=function(){return ue.current.useTransition()};T.version="18.3.1";lo.exports=T;var H=lo.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dc=H,bc=Symbol.for("react.element"),Oc=Symbol.for("react.fragment"),Fc=Object.prototype.hasOwnProperty,Ac=Dc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Uc={key:!0,ref:!0,__self:!0,__source:!0};function po(e,t,n){var r,l={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)Fc.call(t,r)&&!Uc.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:bc,type:e,key:i,ref:a,props:l,_owner:Ac.current}}al.Fragment=Oc;al.jsx=po;al.jsxs=po;ro.exports=al;var s=ro.exports,ho={exports:{}},je={},xo={exports:{}},vo={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(S,P){var M=S.length;S.push(P);e:for(;0<M;){var Q=M-1>>>1,Z=S[Q];if(0<l(Z,P))S[Q]=P,S[M]=Z,M=Q;else break e}}function n(S){return S.length===0?null:S[0]}function r(S){if(S.length===0)return null;var P=S[0],M=S.pop();if(M!==P){S[0]=M;e:for(var Q=0,Z=S.length,lr=Z>>>1;Q<lr;){var vt=2*(Q+1)-1,El=S[vt],gt=vt+1,sr=S[gt];if(0>l(El,M))gt<Z&&0>l(sr,El)?(S[Q]=sr,S[gt]=M,Q=gt):(S[Q]=El,S[vt]=M,Q=vt);else if(gt<Z&&0>l(sr,M))S[Q]=sr,S[gt]=M,Q=gt;else break e}}return P}function l(S,P){var M=S.sortIndex-P.sortIndex;return M!==0?M:S.id-P.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,o=a.now();e.unstable_now=function(){return a.now()-o}}var u=[],d=[],x=1,h=null,p=3,y=!1,j=!1,N=!1,F=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(S){for(var P=n(d);P!==null;){if(P.callback===null)r(d);else if(P.startTime<=S)r(d),P.sortIndex=P.expirationTime,t(u,P);else break;P=n(d)}}function v(S){if(N=!1,m(S),!j)if(n(u)!==null)j=!0,kl(k);else{var P=n(d);P!==null&&Sl(v,P.startTime-S)}}function k(S,P){j=!1,N&&(N=!1,f(_),_=-1),y=!0;var M=p;try{for(m(P),h=n(u);h!==null&&(!(h.expirationTime>P)||S&&!Pe());){var Q=h.callback;if(typeof Q=="function"){h.callback=null,p=h.priorityLevel;var Z=Q(h.expirationTime<=P);P=e.unstable_now(),typeof Z=="function"?h.callback=Z:h===n(u)&&r(u),m(P)}else r(u);h=n(u)}if(h!==null)var lr=!0;else{var vt=n(d);vt!==null&&Sl(v,vt.startTime-P),lr=!1}return lr}finally{h=null,p=M,y=!1}}var E=!1,C=null,_=-1,W=5,z=-1;function Pe(){return!(e.unstable_now()-z<W)}function fn(){if(C!==null){var S=e.unstable_now();z=S;var P=!0;try{P=C(!0,S)}finally{P?mn():(E=!1,C=null)}}else E=!1}var mn;if(typeof c=="function")mn=function(){c(fn)};else if(typeof MessageChannel<"u"){var Hi=new MessageChannel,yc=Hi.port2;Hi.port1.onmessage=fn,mn=function(){yc.postMessage(null)}}else mn=function(){F(fn,0)};function kl(S){C=S,E||(E=!0,mn())}function Sl(S,P){_=F(function(){S(e.unstable_now())},P)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(S){S.callback=null},e.unstable_continueExecution=function(){j||y||(j=!0,kl(k))},e.unstable_forceFrameRate=function(S){0>S||125<S?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):W=0<S?Math.floor(1e3/S):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(S){switch(p){case 1:case 2:case 3:var P=3;break;default:P=p}var M=p;p=P;try{return S()}finally{p=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(S,P){switch(S){case 1:case 2:case 3:case 4:case 5:break;default:S=3}var M=p;p=S;try{return P()}finally{p=M}},e.unstable_scheduleCallback=function(S,P,M){var Q=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?Q+M:Q):M=Q,S){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=M+Z,S={id:x++,callback:P,priorityLevel:S,startTime:M,expirationTime:Z,sortIndex:-1},M>Q?(S.sortIndex=M,t(d,S),n(u)===null&&S===n(d)&&(N?(f(_),_=-1):N=!0,Sl(v,M-Q))):(S.sortIndex=Z,t(u,S),j||y||(j=!0,kl(k))),S},e.unstable_shouldYield=Pe,e.unstable_wrapCallback=function(S){var P=p;return function(){var M=p;p=P;try{return S.apply(this,arguments)}finally{p=M}}}})(vo);xo.exports=vo;var $c=xo.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vc=H,ye=$c;function g(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var go=new Set,bn={};function Rt(e,t){tn(e,t),tn(e+"Capture",t)}function tn(e,t){for(bn[e]=t,e=0;e<t.length;e++)go.add(t[e])}var Qe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ql=Object.prototype.hasOwnProperty,Bc=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Yi={},Gi={};function Hc(e){return ql.call(Gi,e)?!0:ql.call(Yi,e)?!1:Bc.test(e)?Gi[e]=!0:(Yi[e]=!0,!1)}function Wc(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Qc(e,t,n,r){if(t===null||typeof t>"u"||Wc(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ce(e,t,n,r,l,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var ne={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ne[e]=new ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ne[t]=new ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ne[e]=new ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ne[e]=new ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ne[e]=new ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ne[e]=new ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ne[e]=new ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ne[e]=new ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ne[e]=new ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var Xs=/[\-:]([a-z])/g;function Zs(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Xs,Zs);ne[t]=new ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Xs,Zs);ne[t]=new ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Xs,Zs);ne[t]=new ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ne[e]=new ce(e,1,!1,e.toLowerCase(),null,!1,!1)});ne.xlinkHref=new ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ne[e]=new ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function qs(e,t,n,r){var l=ne.hasOwnProperty(t)?ne[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Qc(t,n,l,r)&&(n=null),r||l===null?Hc(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Xe=Vc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ar=Symbol.for("react.element"),bt=Symbol.for("react.portal"),Ot=Symbol.for("react.fragment"),Js=Symbol.for("react.strict_mode"),Jl=Symbol.for("react.profiler"),yo=Symbol.for("react.provider"),jo=Symbol.for("react.context"),ei=Symbol.for("react.forward_ref"),es=Symbol.for("react.suspense"),ts=Symbol.for("react.suspense_list"),ti=Symbol.for("react.memo"),qe=Symbol.for("react.lazy"),No=Symbol.for("react.offscreen"),Xi=Symbol.iterator;function pn(e){return e===null||typeof e!="object"?null:(e=Xi&&e[Xi]||e["@@iterator"],typeof e=="function"?e:null)}var V=Object.assign,_l;function wn(e){if(_l===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);_l=t&&t[1]||""}return`
`+_l+e}var Pl=!1;function Ml(e,t){if(!e||Pl)return"";Pl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(d){var r=d}Reflect.construct(e,[],t)}else{try{t.call()}catch(d){r=d}e.call(t.prototype)}else{try{throw Error()}catch(d){r=d}e()}}catch(d){if(d&&r&&typeof d.stack=="string"){for(var l=d.stack.split(`
`),i=r.stack.split(`
`),a=l.length-1,o=i.length-1;1<=a&&0<=o&&l[a]!==i[o];)o--;for(;1<=a&&0<=o;a--,o--)if(l[a]!==i[o]){if(a!==1||o!==1)do if(a--,o--,0>o||l[a]!==i[o]){var u=`
`+l[a].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=a&&0<=o);break}}}finally{Pl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?wn(e):""}function Kc(e){switch(e.tag){case 5:return wn(e.type);case 16:return wn("Lazy");case 13:return wn("Suspense");case 19:return wn("SuspenseList");case 0:case 2:case 15:return e=Ml(e.type,!1),e;case 11:return e=Ml(e.type.render,!1),e;case 1:return e=Ml(e.type,!0),e;default:return""}}function ns(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ot:return"Fragment";case bt:return"Portal";case Jl:return"Profiler";case Js:return"StrictMode";case es:return"Suspense";case ts:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case jo:return(e.displayName||"Context")+".Consumer";case yo:return(e._context.displayName||"Context")+".Provider";case ei:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ti:return t=e.displayName||null,t!==null?t:ns(e.type)||"Memo";case qe:t=e._payload,e=e._init;try{return ns(e(t))}catch{}}return null}function Yc(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ns(t);case 8:return t===Js?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ft(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function wo(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Gc(e){var t=wo(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function or(e){e._valueTracker||(e._valueTracker=Gc(e))}function ko(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=wo(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function br(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function rs(e,t){var n=t.checked;return V({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Zi(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=ft(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function So(e,t){t=t.checked,t!=null&&qs(e,"checked",t,!1)}function ls(e,t){So(e,t);var n=ft(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ss(e,t.type,n):t.hasOwnProperty("defaultValue")&&ss(e,t.type,ft(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function qi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ss(e,t,n){(t!=="number"||br(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var kn=Array.isArray;function Yt(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ft(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function is(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(g(91));return V({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ji(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(g(92));if(kn(n)){if(1<n.length)throw Error(g(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:ft(n)}}function Eo(e,t){var n=ft(t.value),r=ft(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ea(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Co(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function as(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Co(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ur,_o=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ur=ur||document.createElement("div"),ur.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ur.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function On(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var _n={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xc=["Webkit","ms","Moz","O"];Object.keys(_n).forEach(function(e){Xc.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),_n[t]=_n[e]})});function Po(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||_n.hasOwnProperty(e)&&_n[e]?(""+t).trim():t+"px"}function Mo(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Po(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Zc=V({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function os(e,t){if(t){if(Zc[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(g(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(g(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(g(61))}if(t.style!=null&&typeof t.style!="object")throw Error(g(62))}}function us(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cs=null;function ni(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ds=null,Gt=null,Xt=null;function ta(e){if(e=nr(e)){if(typeof ds!="function")throw Error(g(280));var t=e.stateNode;t&&(t=fl(t),ds(e.stateNode,e.type,t))}}function To(e){Gt?Xt?Xt.push(e):Xt=[e]:Gt=e}function zo(){if(Gt){var e=Gt,t=Xt;if(Xt=Gt=null,ta(e),t)for(e=0;e<t.length;e++)ta(t[e])}}function Ro(e,t){return e(t)}function Io(){}var Tl=!1;function Lo(e,t,n){if(Tl)return e(t,n);Tl=!0;try{return Ro(e,t,n)}finally{Tl=!1,(Gt!==null||Xt!==null)&&(Io(),zo())}}function Fn(e,t){var n=e.stateNode;if(n===null)return null;var r=fl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(g(231,t,typeof n));return n}var fs=!1;if(Qe)try{var hn={};Object.defineProperty(hn,"passive",{get:function(){fs=!0}}),window.addEventListener("test",hn,hn),window.removeEventListener("test",hn,hn)}catch{fs=!1}function qc(e,t,n,r,l,i,a,o,u){var d=Array.prototype.slice.call(arguments,3);try{t.apply(n,d)}catch(x){this.onError(x)}}var Pn=!1,Or=null,Fr=!1,ms=null,Jc={onError:function(e){Pn=!0,Or=e}};function ed(e,t,n,r,l,i,a,o,u){Pn=!1,Or=null,qc.apply(Jc,arguments)}function td(e,t,n,r,l,i,a,o,u){if(ed.apply(this,arguments),Pn){if(Pn){var d=Or;Pn=!1,Or=null}else throw Error(g(198));Fr||(Fr=!0,ms=d)}}function It(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Do(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function na(e){if(It(e)!==e)throw Error(g(188))}function nd(e){var t=e.alternate;if(!t){if(t=It(e),t===null)throw Error(g(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return na(l),e;if(i===r)return na(l),t;i=i.sibling}throw Error(g(188))}if(n.return!==r.return)n=l,r=i;else{for(var a=!1,o=l.child;o;){if(o===n){a=!0,n=l,r=i;break}if(o===r){a=!0,r=l,n=i;break}o=o.sibling}if(!a){for(o=i.child;o;){if(o===n){a=!0,n=i,r=l;break}if(o===r){a=!0,r=i,n=l;break}o=o.sibling}if(!a)throw Error(g(189))}}if(n.alternate!==r)throw Error(g(190))}if(n.tag!==3)throw Error(g(188));return n.stateNode.current===n?e:t}function bo(e){return e=nd(e),e!==null?Oo(e):null}function Oo(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Oo(e);if(t!==null)return t;e=e.sibling}return null}var Fo=ye.unstable_scheduleCallback,ra=ye.unstable_cancelCallback,rd=ye.unstable_shouldYield,ld=ye.unstable_requestPaint,K=ye.unstable_now,sd=ye.unstable_getCurrentPriorityLevel,ri=ye.unstable_ImmediatePriority,Ao=ye.unstable_UserBlockingPriority,Ar=ye.unstable_NormalPriority,id=ye.unstable_LowPriority,Uo=ye.unstable_IdlePriority,ol=null,Ae=null;function ad(e){if(Ae&&typeof Ae.onCommitFiberRoot=="function")try{Ae.onCommitFiberRoot(ol,e,void 0,(e.current.flags&128)===128)}catch{}}var Ie=Math.clz32?Math.clz32:cd,od=Math.log,ud=Math.LN2;function cd(e){return e>>>=0,e===0?32:31-(od(e)/ud|0)|0}var cr=64,dr=4194304;function Sn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ur(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var o=a&~l;o!==0?r=Sn(o):(i&=a,i!==0&&(r=Sn(i)))}else a=n&~l,a!==0?r=Sn(a):i!==0&&(r=Sn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ie(t),l=1<<n,r|=e[n],t&=~l;return r}function dd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function fd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-Ie(i),o=1<<a,u=l[a];u===-1?(!(o&n)||o&r)&&(l[a]=dd(o,t)):u<=t&&(e.expiredLanes|=o),i&=~o}}function ps(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function $o(){var e=cr;return cr<<=1,!(cr&4194240)&&(cr=64),e}function zl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function er(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ie(t),e[t]=n}function md(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Ie(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function li(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ie(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var L=0;function Vo(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Bo,si,Ho,Wo,Qo,hs=!1,fr=[],lt=null,st=null,it=null,An=new Map,Un=new Map,et=[],pd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function la(e,t){switch(e){case"focusin":case"focusout":lt=null;break;case"dragenter":case"dragleave":st=null;break;case"mouseover":case"mouseout":it=null;break;case"pointerover":case"pointerout":An.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Un.delete(t.pointerId)}}function xn(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=nr(t),t!==null&&si(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function hd(e,t,n,r,l){switch(t){case"focusin":return lt=xn(lt,e,t,n,r,l),!0;case"dragenter":return st=xn(st,e,t,n,r,l),!0;case"mouseover":return it=xn(it,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return An.set(i,xn(An.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,Un.set(i,xn(Un.get(i)||null,e,t,n,r,l)),!0}return!1}function Ko(e){var t=Nt(e.target);if(t!==null){var n=It(t);if(n!==null){if(t=n.tag,t===13){if(t=Do(n),t!==null){e.blockedOn=t,Qo(e.priority,function(){Ho(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Er(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=xs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);cs=r,n.target.dispatchEvent(r),cs=null}else return t=nr(n),t!==null&&si(t),e.blockedOn=n,!1;t.shift()}return!0}function sa(e,t,n){Er(e)&&n.delete(t)}function xd(){hs=!1,lt!==null&&Er(lt)&&(lt=null),st!==null&&Er(st)&&(st=null),it!==null&&Er(it)&&(it=null),An.forEach(sa),Un.forEach(sa)}function vn(e,t){e.blockedOn===t&&(e.blockedOn=null,hs||(hs=!0,ye.unstable_scheduleCallback(ye.unstable_NormalPriority,xd)))}function $n(e){function t(l){return vn(l,e)}if(0<fr.length){vn(fr[0],e);for(var n=1;n<fr.length;n++){var r=fr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(lt!==null&&vn(lt,e),st!==null&&vn(st,e),it!==null&&vn(it,e),An.forEach(t),Un.forEach(t),n=0;n<et.length;n++)r=et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<et.length&&(n=et[0],n.blockedOn===null);)Ko(n),n.blockedOn===null&&et.shift()}var Zt=Xe.ReactCurrentBatchConfig,$r=!0;function vd(e,t,n,r){var l=L,i=Zt.transition;Zt.transition=null;try{L=1,ii(e,t,n,r)}finally{L=l,Zt.transition=i}}function gd(e,t,n,r){var l=L,i=Zt.transition;Zt.transition=null;try{L=4,ii(e,t,n,r)}finally{L=l,Zt.transition=i}}function ii(e,t,n,r){if($r){var l=xs(e,t,n,r);if(l===null)$l(e,t,r,Vr,n),la(e,r);else if(hd(l,e,t,n,r))r.stopPropagation();else if(la(e,r),t&4&&-1<pd.indexOf(e)){for(;l!==null;){var i=nr(l);if(i!==null&&Bo(i),i=xs(e,t,n,r),i===null&&$l(e,t,r,Vr,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else $l(e,t,r,null,n)}}var Vr=null;function xs(e,t,n,r){if(Vr=null,e=ni(r),e=Nt(e),e!==null)if(t=It(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Do(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Vr=e,null}function Yo(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(sd()){case ri:return 1;case Ao:return 4;case Ar:case id:return 16;case Uo:return 536870912;default:return 16}default:return 16}}var nt=null,ai=null,Cr=null;function Go(){if(Cr)return Cr;var e,t=ai,n=t.length,r,l="value"in nt?nt.value:nt.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===l[i-r];r++);return Cr=l.slice(e,1<r?1-r:void 0)}function _r(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function mr(){return!0}function ia(){return!1}function Ne(e){function t(n,r,l,i,a){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(i):i[o]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?mr:ia,this.isPropagationStopped=ia,this}return V(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=mr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=mr)},persist:function(){},isPersistent:mr}),t}var cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},oi=Ne(cn),tr=V({},cn,{view:0,detail:0}),yd=Ne(tr),Rl,Il,gn,ul=V({},tr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ui,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==gn&&(gn&&e.type==="mousemove"?(Rl=e.screenX-gn.screenX,Il=e.screenY-gn.screenY):Il=Rl=0,gn=e),Rl)},movementY:function(e){return"movementY"in e?e.movementY:Il}}),aa=Ne(ul),jd=V({},ul,{dataTransfer:0}),Nd=Ne(jd),wd=V({},tr,{relatedTarget:0}),Ll=Ne(wd),kd=V({},cn,{animationName:0,elapsedTime:0,pseudoElement:0}),Sd=Ne(kd),Ed=V({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Cd=Ne(Ed),_d=V({},cn,{data:0}),oa=Ne(_d),Pd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Md={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Td={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function zd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Td[e])?!!t[e]:!1}function ui(){return zd}var Rd=V({},tr,{key:function(e){if(e.key){var t=Pd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=_r(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Md[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ui,charCode:function(e){return e.type==="keypress"?_r(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?_r(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Id=Ne(Rd),Ld=V({},ul,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ua=Ne(Ld),Dd=V({},tr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ui}),bd=Ne(Dd),Od=V({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Fd=Ne(Od),Ad=V({},ul,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ud=Ne(Ad),$d=[9,13,27,32],ci=Qe&&"CompositionEvent"in window,Mn=null;Qe&&"documentMode"in document&&(Mn=document.documentMode);var Vd=Qe&&"TextEvent"in window&&!Mn,Xo=Qe&&(!ci||Mn&&8<Mn&&11>=Mn),ca=" ",da=!1;function Zo(e,t){switch(e){case"keyup":return $d.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function qo(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ft=!1;function Bd(e,t){switch(e){case"compositionend":return qo(t);case"keypress":return t.which!==32?null:(da=!0,ca);case"textInput":return e=t.data,e===ca&&da?null:e;default:return null}}function Hd(e,t){if(Ft)return e==="compositionend"||!ci&&Zo(e,t)?(e=Go(),Cr=ai=nt=null,Ft=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xo&&t.locale!=="ko"?null:t.data;default:return null}}var Wd={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function fa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Wd[e.type]:t==="textarea"}function Jo(e,t,n,r){To(r),t=Br(t,"onChange"),0<t.length&&(n=new oi("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Tn=null,Vn=null;function Qd(e){cu(e,0)}function cl(e){var t=$t(e);if(ko(t))return e}function Kd(e,t){if(e==="change")return t}var eu=!1;if(Qe){var Dl;if(Qe){var bl="oninput"in document;if(!bl){var ma=document.createElement("div");ma.setAttribute("oninput","return;"),bl=typeof ma.oninput=="function"}Dl=bl}else Dl=!1;eu=Dl&&(!document.documentMode||9<document.documentMode)}function pa(){Tn&&(Tn.detachEvent("onpropertychange",tu),Vn=Tn=null)}function tu(e){if(e.propertyName==="value"&&cl(Vn)){var t=[];Jo(t,Vn,e,ni(e)),Lo(Qd,t)}}function Yd(e,t,n){e==="focusin"?(pa(),Tn=t,Vn=n,Tn.attachEvent("onpropertychange",tu)):e==="focusout"&&pa()}function Gd(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return cl(Vn)}function Xd(e,t){if(e==="click")return cl(t)}function Zd(e,t){if(e==="input"||e==="change")return cl(t)}function qd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var De=typeof Object.is=="function"?Object.is:qd;function Bn(e,t){if(De(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!ql.call(t,l)||!De(e[l],t[l]))return!1}return!0}function ha(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xa(e,t){var n=ha(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ha(n)}}function nu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?nu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ru(){for(var e=window,t=br();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=br(e.document)}return t}function di(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Jd(e){var t=ru(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&nu(n.ownerDocument.documentElement,n)){if(r!==null&&di(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=xa(n,i);var a=xa(n,r);l&&a&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ef=Qe&&"documentMode"in document&&11>=document.documentMode,At=null,vs=null,zn=null,gs=!1;function va(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;gs||At==null||At!==br(r)||(r=At,"selectionStart"in r&&di(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zn&&Bn(zn,r)||(zn=r,r=Br(vs,"onSelect"),0<r.length&&(t=new oi("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=At)))}function pr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ut={animationend:pr("Animation","AnimationEnd"),animationiteration:pr("Animation","AnimationIteration"),animationstart:pr("Animation","AnimationStart"),transitionend:pr("Transition","TransitionEnd")},Ol={},lu={};Qe&&(lu=document.createElement("div").style,"AnimationEvent"in window||(delete Ut.animationend.animation,delete Ut.animationiteration.animation,delete Ut.animationstart.animation),"TransitionEvent"in window||delete Ut.transitionend.transition);function dl(e){if(Ol[e])return Ol[e];if(!Ut[e])return e;var t=Ut[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in lu)return Ol[e]=t[n];return e}var su=dl("animationend"),iu=dl("animationiteration"),au=dl("animationstart"),ou=dl("transitionend"),uu=new Map,ga="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function pt(e,t){uu.set(e,t),Rt(t,[e])}for(var Fl=0;Fl<ga.length;Fl++){var Al=ga[Fl],tf=Al.toLowerCase(),nf=Al[0].toUpperCase()+Al.slice(1);pt(tf,"on"+nf)}pt(su,"onAnimationEnd");pt(iu,"onAnimationIteration");pt(au,"onAnimationStart");pt("dblclick","onDoubleClick");pt("focusin","onFocus");pt("focusout","onBlur");pt(ou,"onTransitionEnd");tn("onMouseEnter",["mouseout","mouseover"]);tn("onMouseLeave",["mouseout","mouseover"]);tn("onPointerEnter",["pointerout","pointerover"]);tn("onPointerLeave",["pointerout","pointerover"]);Rt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Rt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Rt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Rt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Rt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Rt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var En="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rf=new Set("cancel close invalid load scroll toggle".split(" ").concat(En));function ya(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,td(r,t,void 0,e),e.currentTarget=null}function cu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var o=r[a],u=o.instance,d=o.currentTarget;if(o=o.listener,u!==i&&l.isPropagationStopped())break e;ya(l,o,d),i=u}else for(a=0;a<r.length;a++){if(o=r[a],u=o.instance,d=o.currentTarget,o=o.listener,u!==i&&l.isPropagationStopped())break e;ya(l,o,d),i=u}}}if(Fr)throw e=ms,Fr=!1,ms=null,e}function b(e,t){var n=t[ks];n===void 0&&(n=t[ks]=new Set);var r=e+"__bubble";n.has(r)||(du(t,e,2,!1),n.add(r))}function Ul(e,t,n){var r=0;t&&(r|=4),du(n,e,r,t)}var hr="_reactListening"+Math.random().toString(36).slice(2);function Hn(e){if(!e[hr]){e[hr]=!0,go.forEach(function(n){n!=="selectionchange"&&(rf.has(n)||Ul(n,!1,e),Ul(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hr]||(t[hr]=!0,Ul("selectionchange",!1,t))}}function du(e,t,n,r){switch(Yo(t)){case 1:var l=vd;break;case 4:l=gd;break;default:l=ii}n=l.bind(null,t,n,e),l=void 0,!fs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function $l(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var o=r.stateNode.containerInfo;if(o===l||o.nodeType===8&&o.parentNode===l)break;if(a===4)for(a=r.return;a!==null;){var u=a.tag;if((u===3||u===4)&&(u=a.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;a=a.return}for(;o!==null;){if(a=Nt(o),a===null)return;if(u=a.tag,u===5||u===6){r=i=a;continue e}o=o.parentNode}}r=r.return}Lo(function(){var d=i,x=ni(n),h=[];e:{var p=uu.get(e);if(p!==void 0){var y=oi,j=e;switch(e){case"keypress":if(_r(n)===0)break e;case"keydown":case"keyup":y=Id;break;case"focusin":j="focus",y=Ll;break;case"focusout":j="blur",y=Ll;break;case"beforeblur":case"afterblur":y=Ll;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=aa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Nd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=bd;break;case su:case iu:case au:y=Sd;break;case ou:y=Fd;break;case"scroll":y=yd;break;case"wheel":y=Ud;break;case"copy":case"cut":case"paste":y=Cd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=ua}var N=(t&4)!==0,F=!N&&e==="scroll",f=N?p!==null?p+"Capture":null:p;N=[];for(var c=d,m;c!==null;){m=c;var v=m.stateNode;if(m.tag===5&&v!==null&&(m=v,f!==null&&(v=Fn(c,f),v!=null&&N.push(Wn(c,v,m)))),F)break;c=c.return}0<N.length&&(p=new y(p,j,null,n,x),h.push({event:p,listeners:N}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",p&&n!==cs&&(j=n.relatedTarget||n.fromElement)&&(Nt(j)||j[Ke]))break e;if((y||p)&&(p=x.window===x?x:(p=x.ownerDocument)?p.defaultView||p.parentWindow:window,y?(j=n.relatedTarget||n.toElement,y=d,j=j?Nt(j):null,j!==null&&(F=It(j),j!==F||j.tag!==5&&j.tag!==6)&&(j=null)):(y=null,j=d),y!==j)){if(N=aa,v="onMouseLeave",f="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(N=ua,v="onPointerLeave",f="onPointerEnter",c="pointer"),F=y==null?p:$t(y),m=j==null?p:$t(j),p=new N(v,c+"leave",y,n,x),p.target=F,p.relatedTarget=m,v=null,Nt(x)===d&&(N=new N(f,c+"enter",j,n,x),N.target=m,N.relatedTarget=F,v=N),F=v,y&&j)t:{for(N=y,f=j,c=0,m=N;m;m=Lt(m))c++;for(m=0,v=f;v;v=Lt(v))m++;for(;0<c-m;)N=Lt(N),c--;for(;0<m-c;)f=Lt(f),m--;for(;c--;){if(N===f||f!==null&&N===f.alternate)break t;N=Lt(N),f=Lt(f)}N=null}else N=null;y!==null&&ja(h,p,y,N,!1),j!==null&&F!==null&&ja(h,F,j,N,!0)}}e:{if(p=d?$t(d):window,y=p.nodeName&&p.nodeName.toLowerCase(),y==="select"||y==="input"&&p.type==="file")var k=Kd;else if(fa(p))if(eu)k=Zd;else{k=Gd;var E=Yd}else(y=p.nodeName)&&y.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(k=Xd);if(k&&(k=k(e,d))){Jo(h,k,n,x);break e}E&&E(e,p,d),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&ss(p,"number",p.value)}switch(E=d?$t(d):window,e){case"focusin":(fa(E)||E.contentEditable==="true")&&(At=E,vs=d,zn=null);break;case"focusout":zn=vs=At=null;break;case"mousedown":gs=!0;break;case"contextmenu":case"mouseup":case"dragend":gs=!1,va(h,n,x);break;case"selectionchange":if(ef)break;case"keydown":case"keyup":va(h,n,x)}var C;if(ci)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Ft?Zo(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Xo&&n.locale!=="ko"&&(Ft||_!=="onCompositionStart"?_==="onCompositionEnd"&&Ft&&(C=Go()):(nt=x,ai="value"in nt?nt.value:nt.textContent,Ft=!0)),E=Br(d,_),0<E.length&&(_=new oa(_,e,null,n,x),h.push({event:_,listeners:E}),C?_.data=C:(C=qo(n),C!==null&&(_.data=C)))),(C=Vd?Bd(e,n):Hd(e,n))&&(d=Br(d,"onBeforeInput"),0<d.length&&(x=new oa("onBeforeInput","beforeinput",null,n,x),h.push({event:x,listeners:d}),x.data=C))}cu(h,t)})}function Wn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Br(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=Fn(e,n),i!=null&&r.unshift(Wn(e,i,l)),i=Fn(e,t),i!=null&&r.push(Wn(e,i,l))),e=e.return}return r}function Lt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ja(e,t,n,r,l){for(var i=t._reactName,a=[];n!==null&&n!==r;){var o=n,u=o.alternate,d=o.stateNode;if(u!==null&&u===r)break;o.tag===5&&d!==null&&(o=d,l?(u=Fn(n,i),u!=null&&a.unshift(Wn(n,u,o))):l||(u=Fn(n,i),u!=null&&a.push(Wn(n,u,o)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var lf=/\r\n?/g,sf=/\u0000|\uFFFD/g;function Na(e){return(typeof e=="string"?e:""+e).replace(lf,`
`).replace(sf,"")}function xr(e,t,n){if(t=Na(t),Na(e)!==t&&n)throw Error(g(425))}function Hr(){}var ys=null,js=null;function Ns(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ws=typeof setTimeout=="function"?setTimeout:void 0,af=typeof clearTimeout=="function"?clearTimeout:void 0,wa=typeof Promise=="function"?Promise:void 0,of=typeof queueMicrotask=="function"?queueMicrotask:typeof wa<"u"?function(e){return wa.resolve(null).then(e).catch(uf)}:ws;function uf(e){setTimeout(function(){throw e})}function Vl(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),$n(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);$n(t)}function at(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ka(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var dn=Math.random().toString(36).slice(2),Fe="__reactFiber$"+dn,Qn="__reactProps$"+dn,Ke="__reactContainer$"+dn,ks="__reactEvents$"+dn,cf="__reactListeners$"+dn,df="__reactHandles$"+dn;function Nt(e){var t=e[Fe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ke]||n[Fe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ka(e);e!==null;){if(n=e[Fe])return n;e=ka(e)}return t}e=n,n=e.parentNode}return null}function nr(e){return e=e[Fe]||e[Ke],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function $t(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(g(33))}function fl(e){return e[Qn]||null}var Ss=[],Vt=-1;function ht(e){return{current:e}}function O(e){0>Vt||(e.current=Ss[Vt],Ss[Vt]=null,Vt--)}function D(e,t){Vt++,Ss[Vt]=e.current,e.current=t}var mt={},ie=ht(mt),me=ht(!1),Ct=mt;function nn(e,t){var n=e.type.contextTypes;if(!n)return mt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function pe(e){return e=e.childContextTypes,e!=null}function Wr(){O(me),O(ie)}function Sa(e,t,n){if(ie.current!==mt)throw Error(g(168));D(ie,t),D(me,n)}function fu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(g(108,Yc(e)||"Unknown",l));return V({},n,r)}function Qr(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||mt,Ct=ie.current,D(ie,e),D(me,me.current),!0}function Ea(e,t,n){var r=e.stateNode;if(!r)throw Error(g(169));n?(e=fu(e,t,Ct),r.__reactInternalMemoizedMergedChildContext=e,O(me),O(ie),D(ie,e)):O(me),D(me,n)}var Ve=null,ml=!1,Bl=!1;function mu(e){Ve===null?Ve=[e]:Ve.push(e)}function ff(e){ml=!0,mu(e)}function xt(){if(!Bl&&Ve!==null){Bl=!0;var e=0,t=L;try{var n=Ve;for(L=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ve=null,ml=!1}catch(l){throw Ve!==null&&(Ve=Ve.slice(e+1)),Fo(ri,xt),l}finally{L=t,Bl=!1}}return null}var Bt=[],Ht=0,Kr=null,Yr=0,we=[],ke=0,_t=null,Be=1,He="";function yt(e,t){Bt[Ht++]=Yr,Bt[Ht++]=Kr,Kr=e,Yr=t}function pu(e,t,n){we[ke++]=Be,we[ke++]=He,we[ke++]=_t,_t=e;var r=Be;e=He;var l=32-Ie(r)-1;r&=~(1<<l),n+=1;var i=32-Ie(t)+l;if(30<i){var a=l-l%5;i=(r&(1<<a)-1).toString(32),r>>=a,l-=a,Be=1<<32-Ie(t)+l|n<<l|r,He=i+e}else Be=1<<i|n<<l|r,He=e}function fi(e){e.return!==null&&(yt(e,1),pu(e,1,0))}function mi(e){for(;e===Kr;)Kr=Bt[--Ht],Bt[Ht]=null,Yr=Bt[--Ht],Bt[Ht]=null;for(;e===_t;)_t=we[--ke],we[ke]=null,He=we[--ke],we[ke]=null,Be=we[--ke],we[ke]=null}var ge=null,ve=null,A=!1,Re=null;function hu(e,t){var n=Se(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ca(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ge=e,ve=at(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ge=e,ve=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=_t!==null?{id:Be,overflow:He}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Se(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ge=e,ve=null,!0):!1;default:return!1}}function Es(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Cs(e){if(A){var t=ve;if(t){var n=t;if(!Ca(e,t)){if(Es(e))throw Error(g(418));t=at(n.nextSibling);var r=ge;t&&Ca(e,t)?hu(r,n):(e.flags=e.flags&-4097|2,A=!1,ge=e)}}else{if(Es(e))throw Error(g(418));e.flags=e.flags&-4097|2,A=!1,ge=e}}}function _a(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ge=e}function vr(e){if(e!==ge)return!1;if(!A)return _a(e),A=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ns(e.type,e.memoizedProps)),t&&(t=ve)){if(Es(e))throw xu(),Error(g(418));for(;t;)hu(e,t),t=at(t.nextSibling)}if(_a(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(g(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ve=at(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ve=null}}else ve=ge?at(e.stateNode.nextSibling):null;return!0}function xu(){for(var e=ve;e;)e=at(e.nextSibling)}function rn(){ve=ge=null,A=!1}function pi(e){Re===null?Re=[e]:Re.push(e)}var mf=Xe.ReactCurrentBatchConfig;function yn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(g(309));var r=n.stateNode}if(!r)throw Error(g(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var o=l.refs;a===null?delete o[i]:o[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(g(284));if(!n._owner)throw Error(g(290,e))}return e}function gr(e,t){throw e=Object.prototype.toString.call(t),Error(g(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Pa(e){var t=e._init;return t(e._payload)}function vu(e){function t(f,c){if(e){var m=f.deletions;m===null?(f.deletions=[c],f.flags|=16):m.push(c)}}function n(f,c){if(!e)return null;for(;c!==null;)t(f,c),c=c.sibling;return null}function r(f,c){for(f=new Map;c!==null;)c.key!==null?f.set(c.key,c):f.set(c.index,c),c=c.sibling;return f}function l(f,c){return f=dt(f,c),f.index=0,f.sibling=null,f}function i(f,c,m){return f.index=m,e?(m=f.alternate,m!==null?(m=m.index,m<c?(f.flags|=2,c):m):(f.flags|=2,c)):(f.flags|=1048576,c)}function a(f){return e&&f.alternate===null&&(f.flags|=2),f}function o(f,c,m,v){return c===null||c.tag!==6?(c=Xl(m,f.mode,v),c.return=f,c):(c=l(c,m),c.return=f,c)}function u(f,c,m,v){var k=m.type;return k===Ot?x(f,c,m.props.children,v,m.key):c!==null&&(c.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===qe&&Pa(k)===c.type)?(v=l(c,m.props),v.ref=yn(f,c,m),v.return=f,v):(v=Lr(m.type,m.key,m.props,null,f.mode,v),v.ref=yn(f,c,m),v.return=f,v)}function d(f,c,m,v){return c===null||c.tag!==4||c.stateNode.containerInfo!==m.containerInfo||c.stateNode.implementation!==m.implementation?(c=Zl(m,f.mode,v),c.return=f,c):(c=l(c,m.children||[]),c.return=f,c)}function x(f,c,m,v,k){return c===null||c.tag!==7?(c=Et(m,f.mode,v,k),c.return=f,c):(c=l(c,m),c.return=f,c)}function h(f,c,m){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Xl(""+c,f.mode,m),c.return=f,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case ar:return m=Lr(c.type,c.key,c.props,null,f.mode,m),m.ref=yn(f,null,c),m.return=f,m;case bt:return c=Zl(c,f.mode,m),c.return=f,c;case qe:var v=c._init;return h(f,v(c._payload),m)}if(kn(c)||pn(c))return c=Et(c,f.mode,m,null),c.return=f,c;gr(f,c)}return null}function p(f,c,m,v){var k=c!==null?c.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return k!==null?null:o(f,c,""+m,v);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ar:return m.key===k?u(f,c,m,v):null;case bt:return m.key===k?d(f,c,m,v):null;case qe:return k=m._init,p(f,c,k(m._payload),v)}if(kn(m)||pn(m))return k!==null?null:x(f,c,m,v,null);gr(f,m)}return null}function y(f,c,m,v,k){if(typeof v=="string"&&v!==""||typeof v=="number")return f=f.get(m)||null,o(c,f,""+v,k);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case ar:return f=f.get(v.key===null?m:v.key)||null,u(c,f,v,k);case bt:return f=f.get(v.key===null?m:v.key)||null,d(c,f,v,k);case qe:var E=v._init;return y(f,c,m,E(v._payload),k)}if(kn(v)||pn(v))return f=f.get(m)||null,x(c,f,v,k,null);gr(c,v)}return null}function j(f,c,m,v){for(var k=null,E=null,C=c,_=c=0,W=null;C!==null&&_<m.length;_++){C.index>_?(W=C,C=null):W=C.sibling;var z=p(f,C,m[_],v);if(z===null){C===null&&(C=W);break}e&&C&&z.alternate===null&&t(f,C),c=i(z,c,_),E===null?k=z:E.sibling=z,E=z,C=W}if(_===m.length)return n(f,C),A&&yt(f,_),k;if(C===null){for(;_<m.length;_++)C=h(f,m[_],v),C!==null&&(c=i(C,c,_),E===null?k=C:E.sibling=C,E=C);return A&&yt(f,_),k}for(C=r(f,C);_<m.length;_++)W=y(C,f,_,m[_],v),W!==null&&(e&&W.alternate!==null&&C.delete(W.key===null?_:W.key),c=i(W,c,_),E===null?k=W:E.sibling=W,E=W);return e&&C.forEach(function(Pe){return t(f,Pe)}),A&&yt(f,_),k}function N(f,c,m,v){var k=pn(m);if(typeof k!="function")throw Error(g(150));if(m=k.call(m),m==null)throw Error(g(151));for(var E=k=null,C=c,_=c=0,W=null,z=m.next();C!==null&&!z.done;_++,z=m.next()){C.index>_?(W=C,C=null):W=C.sibling;var Pe=p(f,C,z.value,v);if(Pe===null){C===null&&(C=W);break}e&&C&&Pe.alternate===null&&t(f,C),c=i(Pe,c,_),E===null?k=Pe:E.sibling=Pe,E=Pe,C=W}if(z.done)return n(f,C),A&&yt(f,_),k;if(C===null){for(;!z.done;_++,z=m.next())z=h(f,z.value,v),z!==null&&(c=i(z,c,_),E===null?k=z:E.sibling=z,E=z);return A&&yt(f,_),k}for(C=r(f,C);!z.done;_++,z=m.next())z=y(C,f,_,z.value,v),z!==null&&(e&&z.alternate!==null&&C.delete(z.key===null?_:z.key),c=i(z,c,_),E===null?k=z:E.sibling=z,E=z);return e&&C.forEach(function(fn){return t(f,fn)}),A&&yt(f,_),k}function F(f,c,m,v){if(typeof m=="object"&&m!==null&&m.type===Ot&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case ar:e:{for(var k=m.key,E=c;E!==null;){if(E.key===k){if(k=m.type,k===Ot){if(E.tag===7){n(f,E.sibling),c=l(E,m.props.children),c.return=f,f=c;break e}}else if(E.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===qe&&Pa(k)===E.type){n(f,E.sibling),c=l(E,m.props),c.ref=yn(f,E,m),c.return=f,f=c;break e}n(f,E);break}else t(f,E);E=E.sibling}m.type===Ot?(c=Et(m.props.children,f.mode,v,m.key),c.return=f,f=c):(v=Lr(m.type,m.key,m.props,null,f.mode,v),v.ref=yn(f,c,m),v.return=f,f=v)}return a(f);case bt:e:{for(E=m.key;c!==null;){if(c.key===E)if(c.tag===4&&c.stateNode.containerInfo===m.containerInfo&&c.stateNode.implementation===m.implementation){n(f,c.sibling),c=l(c,m.children||[]),c.return=f,f=c;break e}else{n(f,c);break}else t(f,c);c=c.sibling}c=Zl(m,f.mode,v),c.return=f,f=c}return a(f);case qe:return E=m._init,F(f,c,E(m._payload),v)}if(kn(m))return j(f,c,m,v);if(pn(m))return N(f,c,m,v);gr(f,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,c!==null&&c.tag===6?(n(f,c.sibling),c=l(c,m),c.return=f,f=c):(n(f,c),c=Xl(m,f.mode,v),c.return=f,f=c),a(f)):n(f,c)}return F}var ln=vu(!0),gu=vu(!1),Gr=ht(null),Xr=null,Wt=null,hi=null;function xi(){hi=Wt=Xr=null}function vi(e){var t=Gr.current;O(Gr),e._currentValue=t}function _s(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function qt(e,t){Xr=e,hi=Wt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(fe=!0),e.firstContext=null)}function Ce(e){var t=e._currentValue;if(hi!==e)if(e={context:e,memoizedValue:t,next:null},Wt===null){if(Xr===null)throw Error(g(308));Wt=e,Xr.dependencies={lanes:0,firstContext:e}}else Wt=Wt.next=e;return t}var wt=null;function gi(e){wt===null?wt=[e]:wt.push(e)}function yu(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,gi(t)):(n.next=l.next,l.next=n),t.interleaved=n,Ye(e,r)}function Ye(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Je=!1;function yi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ju(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function We(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,R&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,Ye(e,n)}return l=r.interleaved,l===null?(t.next=t,gi(r)):(t.next=l.next,l.next=t),r.interleaved=t,Ye(e,n)}function Pr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,li(e,n)}}function Ma(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Zr(e,t,n,r){var l=e.updateQueue;Je=!1;var i=l.firstBaseUpdate,a=l.lastBaseUpdate,o=l.shared.pending;if(o!==null){l.shared.pending=null;var u=o,d=u.next;u.next=null,a===null?i=d:a.next=d,a=u;var x=e.alternate;x!==null&&(x=x.updateQueue,o=x.lastBaseUpdate,o!==a&&(o===null?x.firstBaseUpdate=d:o.next=d,x.lastBaseUpdate=u))}if(i!==null){var h=l.baseState;a=0,x=d=u=null,o=i;do{var p=o.lane,y=o.eventTime;if((r&p)===p){x!==null&&(x=x.next={eventTime:y,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var j=e,N=o;switch(p=t,y=n,N.tag){case 1:if(j=N.payload,typeof j=="function"){h=j.call(y,h,p);break e}h=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=N.payload,p=typeof j=="function"?j.call(y,h,p):j,p==null)break e;h=V({},h,p);break e;case 2:Je=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[o]:p.push(o))}else y={eventTime:y,lane:p,tag:o.tag,payload:o.payload,callback:o.callback,next:null},x===null?(d=x=y,u=h):x=x.next=y,a|=p;if(o=o.next,o===null){if(o=l.shared.pending,o===null)break;p=o,o=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(!0);if(x===null&&(u=h),l.baseState=u,l.firstBaseUpdate=d,l.lastBaseUpdate=x,t=l.shared.interleaved,t!==null){l=t;do a|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);Mt|=a,e.lanes=a,e.memoizedState=h}}function Ta(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(g(191,l));l.call(r)}}}var rr={},Ue=ht(rr),Kn=ht(rr),Yn=ht(rr);function kt(e){if(e===rr)throw Error(g(174));return e}function ji(e,t){switch(D(Yn,t),D(Kn,e),D(Ue,rr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:as(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=as(t,e)}O(Ue),D(Ue,t)}function sn(){O(Ue),O(Kn),O(Yn)}function Nu(e){kt(Yn.current);var t=kt(Ue.current),n=as(t,e.type);t!==n&&(D(Kn,e),D(Ue,n))}function Ni(e){Kn.current===e&&(O(Ue),O(Kn))}var U=ht(0);function qr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Hl=[];function wi(){for(var e=0;e<Hl.length;e++)Hl[e]._workInProgressVersionPrimary=null;Hl.length=0}var Mr=Xe.ReactCurrentDispatcher,Wl=Xe.ReactCurrentBatchConfig,Pt=0,$=null,G=null,q=null,Jr=!1,Rn=!1,Gn=0,pf=0;function re(){throw Error(g(321))}function ki(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!De(e[n],t[n]))return!1;return!0}function Si(e,t,n,r,l,i){if(Pt=i,$=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Mr.current=e===null||e.memoizedState===null?gf:yf,e=n(r,l),Rn){i=0;do{if(Rn=!1,Gn=0,25<=i)throw Error(g(301));i+=1,q=G=null,t.updateQueue=null,Mr.current=jf,e=n(r,l)}while(Rn)}if(Mr.current=el,t=G!==null&&G.next!==null,Pt=0,q=G=$=null,Jr=!1,t)throw Error(g(300));return e}function Ei(){var e=Gn!==0;return Gn=0,e}function Oe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return q===null?$.memoizedState=q=e:q=q.next=e,q}function _e(){if(G===null){var e=$.alternate;e=e!==null?e.memoizedState:null}else e=G.next;var t=q===null?$.memoizedState:q.next;if(t!==null)q=t,G=e;else{if(e===null)throw Error(g(310));G=e,e={memoizedState:G.memoizedState,baseState:G.baseState,baseQueue:G.baseQueue,queue:G.queue,next:null},q===null?$.memoizedState=q=e:q=q.next=e}return q}function Xn(e,t){return typeof t=="function"?t(e):t}function Ql(e){var t=_e(),n=t.queue;if(n===null)throw Error(g(311));n.lastRenderedReducer=e;var r=G,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var a=l.next;l.next=i.next,i.next=a}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var o=a=null,u=null,d=i;do{var x=d.lane;if((Pt&x)===x)u!==null&&(u=u.next={lane:0,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null}),r=d.hasEagerState?d.eagerState:e(r,d.action);else{var h={lane:x,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null};u===null?(o=u=h,a=r):u=u.next=h,$.lanes|=x,Mt|=x}d=d.next}while(d!==null&&d!==i);u===null?a=r:u.next=o,De(r,t.memoizedState)||(fe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,$.lanes|=i,Mt|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Kl(e){var t=_e(),n=t.queue;if(n===null)throw Error(g(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var a=l=l.next;do i=e(i,a.action),a=a.next;while(a!==l);De(i,t.memoizedState)||(fe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function wu(){}function ku(e,t){var n=$,r=_e(),l=t(),i=!De(r.memoizedState,l);if(i&&(r.memoizedState=l,fe=!0),r=r.queue,Ci(Cu.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||q!==null&&q.memoizedState.tag&1){if(n.flags|=2048,Zn(9,Eu.bind(null,n,r,l,t),void 0,null),J===null)throw Error(g(349));Pt&30||Su(n,t,l)}return l}function Su(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=$.updateQueue,t===null?(t={lastEffect:null,stores:null},$.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Eu(e,t,n,r){t.value=n,t.getSnapshot=r,_u(t)&&Pu(e)}function Cu(e,t,n){return n(function(){_u(t)&&Pu(e)})}function _u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!De(e,n)}catch{return!0}}function Pu(e){var t=Ye(e,1);t!==null&&Le(t,e,1,-1)}function za(e){var t=Oe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Xn,lastRenderedState:e},t.queue=e,e=e.dispatch=vf.bind(null,$,e),[t.memoizedState,e]}function Zn(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=$.updateQueue,t===null?(t={lastEffect:null,stores:null},$.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Mu(){return _e().memoizedState}function Tr(e,t,n,r){var l=Oe();$.flags|=e,l.memoizedState=Zn(1|t,n,void 0,r===void 0?null:r)}function pl(e,t,n,r){var l=_e();r=r===void 0?null:r;var i=void 0;if(G!==null){var a=G.memoizedState;if(i=a.destroy,r!==null&&ki(r,a.deps)){l.memoizedState=Zn(t,n,i,r);return}}$.flags|=e,l.memoizedState=Zn(1|t,n,i,r)}function Ra(e,t){return Tr(8390656,8,e,t)}function Ci(e,t){return pl(2048,8,e,t)}function Tu(e,t){return pl(4,2,e,t)}function zu(e,t){return pl(4,4,e,t)}function Ru(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Iu(e,t,n){return n=n!=null?n.concat([e]):null,pl(4,4,Ru.bind(null,t,e),n)}function _i(){}function Lu(e,t){var n=_e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ki(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Du(e,t){var n=_e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ki(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function bu(e,t,n){return Pt&21?(De(n,t)||(n=$o(),$.lanes|=n,Mt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,fe=!0),e.memoizedState=n)}function hf(e,t){var n=L;L=n!==0&&4>n?n:4,e(!0);var r=Wl.transition;Wl.transition={};try{e(!1),t()}finally{L=n,Wl.transition=r}}function Ou(){return _e().memoizedState}function xf(e,t,n){var r=ct(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Fu(e))Au(t,n);else if(n=yu(e,t,n,r),n!==null){var l=oe();Le(n,e,r,l),Uu(n,t,r)}}function vf(e,t,n){var r=ct(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Fu(e))Au(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,o=i(a,n);if(l.hasEagerState=!0,l.eagerState=o,De(o,a)){var u=t.interleaved;u===null?(l.next=l,gi(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=yu(e,t,l,r),n!==null&&(l=oe(),Le(n,e,r,l),Uu(n,t,r))}}function Fu(e){var t=e.alternate;return e===$||t!==null&&t===$}function Au(e,t){Rn=Jr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Uu(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,li(e,n)}}var el={readContext:Ce,useCallback:re,useContext:re,useEffect:re,useImperativeHandle:re,useInsertionEffect:re,useLayoutEffect:re,useMemo:re,useReducer:re,useRef:re,useState:re,useDebugValue:re,useDeferredValue:re,useTransition:re,useMutableSource:re,useSyncExternalStore:re,useId:re,unstable_isNewReconciler:!1},gf={readContext:Ce,useCallback:function(e,t){return Oe().memoizedState=[e,t===void 0?null:t],e},useContext:Ce,useEffect:Ra,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Tr(4194308,4,Ru.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Tr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Tr(4,2,e,t)},useMemo:function(e,t){var n=Oe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Oe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=xf.bind(null,$,e),[r.memoizedState,e]},useRef:function(e){var t=Oe();return e={current:e},t.memoizedState=e},useState:za,useDebugValue:_i,useDeferredValue:function(e){return Oe().memoizedState=e},useTransition:function(){var e=za(!1),t=e[0];return e=hf.bind(null,e[1]),Oe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=$,l=Oe();if(A){if(n===void 0)throw Error(g(407));n=n()}else{if(n=t(),J===null)throw Error(g(349));Pt&30||Su(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,Ra(Cu.bind(null,r,i,e),[e]),r.flags|=2048,Zn(9,Eu.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Oe(),t=J.identifierPrefix;if(A){var n=He,r=Be;n=(r&~(1<<32-Ie(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Gn++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=pf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},yf={readContext:Ce,useCallback:Lu,useContext:Ce,useEffect:Ci,useImperativeHandle:Iu,useInsertionEffect:Tu,useLayoutEffect:zu,useMemo:Du,useReducer:Ql,useRef:Mu,useState:function(){return Ql(Xn)},useDebugValue:_i,useDeferredValue:function(e){var t=_e();return bu(t,G.memoizedState,e)},useTransition:function(){var e=Ql(Xn)[0],t=_e().memoizedState;return[e,t]},useMutableSource:wu,useSyncExternalStore:ku,useId:Ou,unstable_isNewReconciler:!1},jf={readContext:Ce,useCallback:Lu,useContext:Ce,useEffect:Ci,useImperativeHandle:Iu,useInsertionEffect:Tu,useLayoutEffect:zu,useMemo:Du,useReducer:Kl,useRef:Mu,useState:function(){return Kl(Xn)},useDebugValue:_i,useDeferredValue:function(e){var t=_e();return G===null?t.memoizedState=e:bu(t,G.memoizedState,e)},useTransition:function(){var e=Kl(Xn)[0],t=_e().memoizedState;return[e,t]},useMutableSource:wu,useSyncExternalStore:ku,useId:Ou,unstable_isNewReconciler:!1};function Te(e,t){if(e&&e.defaultProps){t=V({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ps(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:V({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var hl={isMounted:function(e){return(e=e._reactInternals)?It(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=oe(),l=ct(e),i=We(r,l);i.payload=t,n!=null&&(i.callback=n),t=ot(e,i,l),t!==null&&(Le(t,e,l,r),Pr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=oe(),l=ct(e),i=We(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=ot(e,i,l),t!==null&&(Le(t,e,l,r),Pr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=oe(),r=ct(e),l=We(n,r);l.tag=2,t!=null&&(l.callback=t),t=ot(e,l,r),t!==null&&(Le(t,e,r,n),Pr(t,e,r))}};function Ia(e,t,n,r,l,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!Bn(n,r)||!Bn(l,i):!0}function $u(e,t,n){var r=!1,l=mt,i=t.contextType;return typeof i=="object"&&i!==null?i=Ce(i):(l=pe(t)?Ct:ie.current,r=t.contextTypes,i=(r=r!=null)?nn(e,l):mt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=hl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function La(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&hl.enqueueReplaceState(t,t.state,null)}function Ms(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},yi(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=Ce(i):(i=pe(t)?Ct:ie.current,l.context=nn(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ps(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&hl.enqueueReplaceState(l,l.state,null),Zr(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function an(e,t){try{var n="",r=t;do n+=Kc(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function Yl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ts(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Nf=typeof WeakMap=="function"?WeakMap:Map;function Vu(e,t,n){n=We(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){nl||(nl=!0,Us=r),Ts(e,t)},n}function Bu(e,t,n){n=We(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Ts(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ts(e,t),typeof r!="function"&&(ut===null?ut=new Set([this]):ut.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Da(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Nf;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Df.bind(null,e,t,n),t.then(e,e))}function ba(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Oa(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=We(-1,1),t.tag=2,ot(n,t,1))),n.lanes|=1),e)}var wf=Xe.ReactCurrentOwner,fe=!1;function ae(e,t,n,r){t.child=e===null?gu(t,null,n,r):ln(t,e.child,n,r)}function Fa(e,t,n,r,l){n=n.render;var i=t.ref;return qt(t,l),r=Si(e,t,n,r,i,l),n=Ei(),e!==null&&!fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Ge(e,t,l)):(A&&n&&fi(t),t.flags|=1,ae(e,t,r,l),t.child)}function Aa(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!Di(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Hu(e,t,i,r,l)):(e=Lr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:Bn,n(a,r)&&e.ref===t.ref)return Ge(e,t,l)}return t.flags|=1,e=dt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Hu(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(Bn(i,r)&&e.ref===t.ref)if(fe=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(fe=!0);else return t.lanes=e.lanes,Ge(e,t,l)}return zs(e,t,n,r,l)}function Wu(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},D(Kt,xe),xe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,D(Kt,xe),xe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,D(Kt,xe),xe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,D(Kt,xe),xe|=r;return ae(e,t,l,n),t.child}function Qu(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function zs(e,t,n,r,l){var i=pe(n)?Ct:ie.current;return i=nn(t,i),qt(t,l),n=Si(e,t,n,r,i,l),r=Ei(),e!==null&&!fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Ge(e,t,l)):(A&&r&&fi(t),t.flags|=1,ae(e,t,n,l),t.child)}function Ua(e,t,n,r,l){if(pe(n)){var i=!0;Qr(t)}else i=!1;if(qt(t,l),t.stateNode===null)zr(e,t),$u(t,n,r),Ms(t,n,r,l),r=!0;else if(e===null){var a=t.stateNode,o=t.memoizedProps;a.props=o;var u=a.context,d=n.contextType;typeof d=="object"&&d!==null?d=Ce(d):(d=pe(n)?Ct:ie.current,d=nn(t,d));var x=n.getDerivedStateFromProps,h=typeof x=="function"||typeof a.getSnapshotBeforeUpdate=="function";h||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==r||u!==d)&&La(t,a,r,d),Je=!1;var p=t.memoizedState;a.state=p,Zr(t,r,a,l),u=t.memoizedState,o!==r||p!==u||me.current||Je?(typeof x=="function"&&(Ps(t,n,x,r),u=t.memoizedState),(o=Je||Ia(t,n,o,r,p,u,d))?(h||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),a.props=r,a.state=u,a.context=d,r=o):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,ju(e,t),o=t.memoizedProps,d=t.type===t.elementType?o:Te(t.type,o),a.props=d,h=t.pendingProps,p=a.context,u=n.contextType,typeof u=="object"&&u!==null?u=Ce(u):(u=pe(n)?Ct:ie.current,u=nn(t,u));var y=n.getDerivedStateFromProps;(x=typeof y=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==h||p!==u)&&La(t,a,r,u),Je=!1,p=t.memoizedState,a.state=p,Zr(t,r,a,l);var j=t.memoizedState;o!==h||p!==j||me.current||Je?(typeof y=="function"&&(Ps(t,n,y,r),j=t.memoizedState),(d=Je||Ia(t,n,d,r,p,j,u)||!1)?(x||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,j,u),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,j,u)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=j),a.props=r,a.state=j,a.context=u,r=d):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Rs(e,t,n,r,i,l)}function Rs(e,t,n,r,l,i){Qu(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return l&&Ea(t,n,!1),Ge(e,t,i);r=t.stateNode,wf.current=t;var o=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=ln(t,e.child,null,i),t.child=ln(t,null,o,i)):ae(e,t,o,i),t.memoizedState=r.state,l&&Ea(t,n,!0),t.child}function Ku(e){var t=e.stateNode;t.pendingContext?Sa(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Sa(e,t.context,!1),ji(e,t.containerInfo)}function $a(e,t,n,r,l){return rn(),pi(l),t.flags|=256,ae(e,t,n,r),t.child}var Is={dehydrated:null,treeContext:null,retryLane:0};function Ls(e){return{baseLanes:e,cachePool:null,transitions:null}}function Yu(e,t,n){var r=t.pendingProps,l=U.current,i=!1,a=(t.flags&128)!==0,o;if((o=a)||(o=e!==null&&e.memoizedState===null?!1:(l&2)!==0),o?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),D(U,l&1),e===null)return Cs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=gl(a,r,0,null),e=Et(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ls(n),t.memoizedState=Is,e):Pi(t,a));if(l=e.memoizedState,l!==null&&(o=l.dehydrated,o!==null))return kf(e,t,a,r,o,l,n);if(i){i=r.fallback,a=t.mode,l=e.child,o=l.sibling;var u={mode:"hidden",children:r.children};return!(a&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=dt(l,u),r.subtreeFlags=l.subtreeFlags&14680064),o!==null?i=dt(o,i):(i=Et(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?Ls(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=Is,r}return i=e.child,e=i.sibling,r=dt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Pi(e,t){return t=gl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function yr(e,t,n,r){return r!==null&&pi(r),ln(t,e.child,null,n),e=Pi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function kf(e,t,n,r,l,i,a){if(n)return t.flags&256?(t.flags&=-257,r=Yl(Error(g(422))),yr(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=gl({mode:"visible",children:r.children},l,0,null),i=Et(i,l,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&ln(t,e.child,null,a),t.child.memoizedState=Ls(a),t.memoizedState=Is,i);if(!(t.mode&1))return yr(e,t,a,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var o=r.dgst;return r=o,i=Error(g(419)),r=Yl(i,r,void 0),yr(e,t,a,r)}if(o=(a&e.childLanes)!==0,fe||o){if(r=J,r!==null){switch(a&-a){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|a)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,Ye(e,l),Le(r,e,l,-1))}return Li(),r=Yl(Error(g(421))),yr(e,t,a,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=bf.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,ve=at(l.nextSibling),ge=t,A=!0,Re=null,e!==null&&(we[ke++]=Be,we[ke++]=He,we[ke++]=_t,Be=e.id,He=e.overflow,_t=t),t=Pi(t,r.children),t.flags|=4096,t)}function Va(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),_s(e.return,t,n)}function Gl(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function Gu(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(ae(e,t,r.children,n),r=U.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Va(e,n,t);else if(e.tag===19)Va(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(D(U,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&qr(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Gl(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&qr(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Gl(t,!0,n,null,i);break;case"together":Gl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function zr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ge(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Mt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(g(153));if(t.child!==null){for(e=t.child,n=dt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=dt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Sf(e,t,n){switch(t.tag){case 3:Ku(t),rn();break;case 5:Nu(t);break;case 1:pe(t.type)&&Qr(t);break;case 4:ji(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;D(Gr,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(D(U,U.current&1),t.flags|=128,null):n&t.child.childLanes?Yu(e,t,n):(D(U,U.current&1),e=Ge(e,t,n),e!==null?e.sibling:null);D(U,U.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Gu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),D(U,U.current),r)break;return null;case 22:case 23:return t.lanes=0,Wu(e,t,n)}return Ge(e,t,n)}var Xu,Ds,Zu,qu;Xu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ds=function(){};Zu=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,kt(Ue.current);var i=null;switch(n){case"input":l=rs(e,l),r=rs(e,r),i=[];break;case"select":l=V({},l,{value:void 0}),r=V({},r,{value:void 0}),i=[];break;case"textarea":l=is(e,l),r=is(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Hr)}os(n,r);var a;n=null;for(d in l)if(!r.hasOwnProperty(d)&&l.hasOwnProperty(d)&&l[d]!=null)if(d==="style"){var o=l[d];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else d!=="dangerouslySetInnerHTML"&&d!=="children"&&d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(bn.hasOwnProperty(d)?i||(i=[]):(i=i||[]).push(d,null));for(d in r){var u=r[d];if(o=l!=null?l[d]:void 0,r.hasOwnProperty(d)&&u!==o&&(u!=null||o!=null))if(d==="style")if(o){for(a in o)!o.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&o[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(i||(i=[]),i.push(d,n)),n=u;else d==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,o=o?o.__html:void 0,u!=null&&o!==u&&(i=i||[]).push(d,u)):d==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(d,""+u):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&(bn.hasOwnProperty(d)?(u!=null&&d==="onScroll"&&b("scroll",e),i||o===u||(i=[])):(i=i||[]).push(d,u))}n&&(i=i||[]).push("style",n);var d=i;(t.updateQueue=d)&&(t.flags|=4)}};qu=function(e,t,n,r){n!==r&&(t.flags|=4)};function jn(e,t){if(!A)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function le(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ef(e,t,n){var r=t.pendingProps;switch(mi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return le(t),null;case 1:return pe(t.type)&&Wr(),le(t),null;case 3:return r=t.stateNode,sn(),O(me),O(ie),wi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(vr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Re!==null&&(Bs(Re),Re=null))),Ds(e,t),le(t),null;case 5:Ni(t);var l=kt(Yn.current);if(n=t.type,e!==null&&t.stateNode!=null)Zu(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(g(166));return le(t),null}if(e=kt(Ue.current),vr(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Fe]=t,r[Qn]=i,e=(t.mode&1)!==0,n){case"dialog":b("cancel",r),b("close",r);break;case"iframe":case"object":case"embed":b("load",r);break;case"video":case"audio":for(l=0;l<En.length;l++)b(En[l],r);break;case"source":b("error",r);break;case"img":case"image":case"link":b("error",r),b("load",r);break;case"details":b("toggle",r);break;case"input":Zi(r,i),b("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},b("invalid",r);break;case"textarea":Ji(r,i),b("invalid",r)}os(n,i),l=null;for(var a in i)if(i.hasOwnProperty(a)){var o=i[a];a==="children"?typeof o=="string"?r.textContent!==o&&(i.suppressHydrationWarning!==!0&&xr(r.textContent,o,e),l=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(i.suppressHydrationWarning!==!0&&xr(r.textContent,o,e),l=["children",""+o]):bn.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&b("scroll",r)}switch(n){case"input":or(r),qi(r,i,!0);break;case"textarea":or(r),ea(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Hr)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Co(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[Fe]=t,e[Qn]=r,Xu(e,t,!1,!1),t.stateNode=e;e:{switch(a=us(n,r),n){case"dialog":b("cancel",e),b("close",e),l=r;break;case"iframe":case"object":case"embed":b("load",e),l=r;break;case"video":case"audio":for(l=0;l<En.length;l++)b(En[l],e);l=r;break;case"source":b("error",e),l=r;break;case"img":case"image":case"link":b("error",e),b("load",e),l=r;break;case"details":b("toggle",e),l=r;break;case"input":Zi(e,r),l=rs(e,r),b("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=V({},r,{value:void 0}),b("invalid",e);break;case"textarea":Ji(e,r),l=is(e,r),b("invalid",e);break;default:l=r}os(n,l),o=l;for(i in o)if(o.hasOwnProperty(i)){var u=o[i];i==="style"?Mo(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&_o(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&On(e,u):typeof u=="number"&&On(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(bn.hasOwnProperty(i)?u!=null&&i==="onScroll"&&b("scroll",e):u!=null&&qs(e,i,u,a))}switch(n){case"input":or(e),qi(e,r,!1);break;case"textarea":or(e),ea(e);break;case"option":r.value!=null&&e.setAttribute("value",""+ft(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Yt(e,!!r.multiple,i,!1):r.defaultValue!=null&&Yt(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Hr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return le(t),null;case 6:if(e&&t.stateNode!=null)qu(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(g(166));if(n=kt(Yn.current),kt(Ue.current),vr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Fe]=t,(i=r.nodeValue!==n)&&(e=ge,e!==null))switch(e.tag){case 3:xr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&xr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Fe]=t,t.stateNode=r}return le(t),null;case 13:if(O(U),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(A&&ve!==null&&t.mode&1&&!(t.flags&128))xu(),rn(),t.flags|=98560,i=!1;else if(i=vr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(g(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(g(317));i[Fe]=t}else rn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;le(t),i=!1}else Re!==null&&(Bs(Re),Re=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||U.current&1?X===0&&(X=3):Li())),t.updateQueue!==null&&(t.flags|=4),le(t),null);case 4:return sn(),Ds(e,t),e===null&&Hn(t.stateNode.containerInfo),le(t),null;case 10:return vi(t.type._context),le(t),null;case 17:return pe(t.type)&&Wr(),le(t),null;case 19:if(O(U),i=t.memoizedState,i===null)return le(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)jn(i,!1);else{if(X!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=qr(e),a!==null){for(t.flags|=128,jn(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return D(U,U.current&1|2),t.child}e=e.sibling}i.tail!==null&&K()>on&&(t.flags|=128,r=!0,jn(i,!1),t.lanes=4194304)}else{if(!r)if(e=qr(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),jn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!A)return le(t),null}else 2*K()-i.renderingStartTime>on&&n!==1073741824&&(t.flags|=128,r=!0,jn(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=K(),t.sibling=null,n=U.current,D(U,r?n&1|2:n&1),t):(le(t),null);case 22:case 23:return Ii(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?xe&1073741824&&(le(t),t.subtreeFlags&6&&(t.flags|=8192)):le(t),null;case 24:return null;case 25:return null}throw Error(g(156,t.tag))}function Cf(e,t){switch(mi(t),t.tag){case 1:return pe(t.type)&&Wr(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return sn(),O(me),O(ie),wi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ni(t),null;case 13:if(O(U),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(g(340));rn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return O(U),null;case 4:return sn(),null;case 10:return vi(t.type._context),null;case 22:case 23:return Ii(),null;case 24:return null;default:return null}}var jr=!1,se=!1,_f=typeof WeakSet=="function"?WeakSet:Set,w=null;function Qt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){B(e,t,r)}else n.current=null}function bs(e,t,n){try{n()}catch(r){B(e,t,r)}}var Ba=!1;function Pf(e,t){if(ys=$r,e=ru(),di(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,o=-1,u=-1,d=0,x=0,h=e,p=null;t:for(;;){for(var y;h!==n||l!==0&&h.nodeType!==3||(o=a+l),h!==i||r!==0&&h.nodeType!==3||(u=a+r),h.nodeType===3&&(a+=h.nodeValue.length),(y=h.firstChild)!==null;)p=h,h=y;for(;;){if(h===e)break t;if(p===n&&++d===l&&(o=a),p===i&&++x===r&&(u=a),(y=h.nextSibling)!==null)break;h=p,p=h.parentNode}h=y}n=o===-1||u===-1?null:{start:o,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(js={focusedElem:e,selectionRange:n},$r=!1,w=t;w!==null;)if(t=w,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,w=e;else for(;w!==null;){t=w;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var N=j.memoizedProps,F=j.memoizedState,f=t.stateNode,c=f.getSnapshotBeforeUpdate(t.elementType===t.type?N:Te(t.type,N),F);f.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(g(163))}}catch(v){B(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,w=e;break}w=t.return}return j=Ba,Ba=!1,j}function In(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&bs(t,n,i)}l=l.next}while(l!==r)}}function xl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Os(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ju(e){var t=e.alternate;t!==null&&(e.alternate=null,Ju(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Fe],delete t[Qn],delete t[ks],delete t[cf],delete t[df])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ec(e){return e.tag===5||e.tag===3||e.tag===4}function Ha(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ec(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Fs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Hr));else if(r!==4&&(e=e.child,e!==null))for(Fs(e,t,n),e=e.sibling;e!==null;)Fs(e,t,n),e=e.sibling}function As(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(As(e,t,n),e=e.sibling;e!==null;)As(e,t,n),e=e.sibling}var ee=null,ze=!1;function Ze(e,t,n){for(n=n.child;n!==null;)tc(e,t,n),n=n.sibling}function tc(e,t,n){if(Ae&&typeof Ae.onCommitFiberUnmount=="function")try{Ae.onCommitFiberUnmount(ol,n)}catch{}switch(n.tag){case 5:se||Qt(n,t);case 6:var r=ee,l=ze;ee=null,Ze(e,t,n),ee=r,ze=l,ee!==null&&(ze?(e=ee,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ee.removeChild(n.stateNode));break;case 18:ee!==null&&(ze?(e=ee,n=n.stateNode,e.nodeType===8?Vl(e.parentNode,n):e.nodeType===1&&Vl(e,n),$n(e)):Vl(ee,n.stateNode));break;case 4:r=ee,l=ze,ee=n.stateNode.containerInfo,ze=!0,Ze(e,t,n),ee=r,ze=l;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&bs(n,t,a),l=l.next}while(l!==r)}Ze(e,t,n);break;case 1:if(!se&&(Qt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){B(n,t,o)}Ze(e,t,n);break;case 21:Ze(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,Ze(e,t,n),se=r):Ze(e,t,n);break;default:Ze(e,t,n)}}function Wa(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new _f),t.forEach(function(r){var l=Of.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Me(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,a=t,o=a;e:for(;o!==null;){switch(o.tag){case 5:ee=o.stateNode,ze=!1;break e;case 3:ee=o.stateNode.containerInfo,ze=!0;break e;case 4:ee=o.stateNode.containerInfo,ze=!0;break e}o=o.return}if(ee===null)throw Error(g(160));tc(i,a,l),ee=null,ze=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(d){B(l,t,d)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)nc(t,e),t=t.sibling}function nc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Me(t,e),be(e),r&4){try{In(3,e,e.return),xl(3,e)}catch(N){B(e,e.return,N)}try{In(5,e,e.return)}catch(N){B(e,e.return,N)}}break;case 1:Me(t,e),be(e),r&512&&n!==null&&Qt(n,n.return);break;case 5:if(Me(t,e),be(e),r&512&&n!==null&&Qt(n,n.return),e.flags&32){var l=e.stateNode;try{On(l,"")}catch(N){B(e,e.return,N)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,o=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{o==="input"&&i.type==="radio"&&i.name!=null&&So(l,i),us(o,a);var d=us(o,i);for(a=0;a<u.length;a+=2){var x=u[a],h=u[a+1];x==="style"?Mo(l,h):x==="dangerouslySetInnerHTML"?_o(l,h):x==="children"?On(l,h):qs(l,x,h,d)}switch(o){case"input":ls(l,i);break;case"textarea":Eo(l,i);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?Yt(l,!!i.multiple,y,!1):p!==!!i.multiple&&(i.defaultValue!=null?Yt(l,!!i.multiple,i.defaultValue,!0):Yt(l,!!i.multiple,i.multiple?[]:"",!1))}l[Qn]=i}catch(N){B(e,e.return,N)}}break;case 6:if(Me(t,e),be(e),r&4){if(e.stateNode===null)throw Error(g(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(N){B(e,e.return,N)}}break;case 3:if(Me(t,e),be(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{$n(t.containerInfo)}catch(N){B(e,e.return,N)}break;case 4:Me(t,e),be(e);break;case 13:Me(t,e),be(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(zi=K())),r&4&&Wa(e);break;case 22:if(x=n!==null&&n.memoizedState!==null,e.mode&1?(se=(d=se)||x,Me(t,e),se=d):Me(t,e),be(e),r&8192){if(d=e.memoizedState!==null,(e.stateNode.isHidden=d)&&!x&&e.mode&1)for(w=e,x=e.child;x!==null;){for(h=w=x;w!==null;){switch(p=w,y=p.child,p.tag){case 0:case 11:case 14:case 15:In(4,p,p.return);break;case 1:Qt(p,p.return);var j=p.stateNode;if(typeof j.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(N){B(r,n,N)}}break;case 5:Qt(p,p.return);break;case 22:if(p.memoizedState!==null){Ka(h);continue}}y!==null?(y.return=p,w=y):Ka(h)}x=x.sibling}e:for(x=null,h=e;;){if(h.tag===5){if(x===null){x=h;try{l=h.stateNode,d?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(o=h.stateNode,u=h.memoizedProps.style,a=u!=null&&u.hasOwnProperty("display")?u.display:null,o.style.display=Po("display",a))}catch(N){B(e,e.return,N)}}}else if(h.tag===6){if(x===null)try{h.stateNode.nodeValue=d?"":h.memoizedProps}catch(N){B(e,e.return,N)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;x===h&&(x=null),h=h.return}x===h&&(x=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Me(t,e),be(e),r&4&&Wa(e);break;case 21:break;default:Me(t,e),be(e)}}function be(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ec(n)){var r=n;break e}n=n.return}throw Error(g(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(On(l,""),r.flags&=-33);var i=Ha(e);As(e,i,l);break;case 3:case 4:var a=r.stateNode.containerInfo,o=Ha(e);Fs(e,o,a);break;default:throw Error(g(161))}}catch(u){B(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Mf(e,t,n){w=e,rc(e)}function rc(e,t,n){for(var r=(e.mode&1)!==0;w!==null;){var l=w,i=l.child;if(l.tag===22&&r){var a=l.memoizedState!==null||jr;if(!a){var o=l.alternate,u=o!==null&&o.memoizedState!==null||se;o=jr;var d=se;if(jr=a,(se=u)&&!d)for(w=l;w!==null;)a=w,u=a.child,a.tag===22&&a.memoizedState!==null?Ya(l):u!==null?(u.return=a,w=u):Ya(l);for(;i!==null;)w=i,rc(i),i=i.sibling;w=l,jr=o,se=d}Qa(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,w=i):Qa(e)}}function Qa(e){for(;w!==null;){var t=w;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||xl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Te(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Ta(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ta(t,a,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var d=t.alternate;if(d!==null){var x=d.memoizedState;if(x!==null){var h=x.dehydrated;h!==null&&$n(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(g(163))}se||t.flags&512&&Os(t)}catch(p){B(t,t.return,p)}}if(t===e){w=null;break}if(n=t.sibling,n!==null){n.return=t.return,w=n;break}w=t.return}}function Ka(e){for(;w!==null;){var t=w;if(t===e){w=null;break}var n=t.sibling;if(n!==null){n.return=t.return,w=n;break}w=t.return}}function Ya(e){for(;w!==null;){var t=w;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{xl(4,t)}catch(u){B(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){B(t,l,u)}}var i=t.return;try{Os(t)}catch(u){B(t,i,u)}break;case 5:var a=t.return;try{Os(t)}catch(u){B(t,a,u)}}}catch(u){B(t,t.return,u)}if(t===e){w=null;break}var o=t.sibling;if(o!==null){o.return=t.return,w=o;break}w=t.return}}var Tf=Math.ceil,tl=Xe.ReactCurrentDispatcher,Mi=Xe.ReactCurrentOwner,Ee=Xe.ReactCurrentBatchConfig,R=0,J=null,Y=null,te=0,xe=0,Kt=ht(0),X=0,qn=null,Mt=0,vl=0,Ti=0,Ln=null,de=null,zi=0,on=1/0,$e=null,nl=!1,Us=null,ut=null,Nr=!1,rt=null,rl=0,Dn=0,$s=null,Rr=-1,Ir=0;function oe(){return R&6?K():Rr!==-1?Rr:Rr=K()}function ct(e){return e.mode&1?R&2&&te!==0?te&-te:mf.transition!==null?(Ir===0&&(Ir=$o()),Ir):(e=L,e!==0||(e=window.event,e=e===void 0?16:Yo(e.type)),e):1}function Le(e,t,n,r){if(50<Dn)throw Dn=0,$s=null,Error(g(185));er(e,n,r),(!(R&2)||e!==J)&&(e===J&&(!(R&2)&&(vl|=n),X===4&&tt(e,te)),he(e,r),n===1&&R===0&&!(t.mode&1)&&(on=K()+500,ml&&xt()))}function he(e,t){var n=e.callbackNode;fd(e,t);var r=Ur(e,e===J?te:0);if(r===0)n!==null&&ra(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ra(n),t===1)e.tag===0?ff(Ga.bind(null,e)):mu(Ga.bind(null,e)),of(function(){!(R&6)&&xt()}),n=null;else{switch(Vo(r)){case 1:n=ri;break;case 4:n=Ao;break;case 16:n=Ar;break;case 536870912:n=Uo;break;default:n=Ar}n=dc(n,lc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function lc(e,t){if(Rr=-1,Ir=0,R&6)throw Error(g(327));var n=e.callbackNode;if(Jt()&&e.callbackNode!==n)return null;var r=Ur(e,e===J?te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ll(e,r);else{t=r;var l=R;R|=2;var i=ic();(J!==e||te!==t)&&($e=null,on=K()+500,St(e,t));do try{If();break}catch(o){sc(e,o)}while(!0);xi(),tl.current=i,R=l,Y!==null?t=0:(J=null,te=0,t=X)}if(t!==0){if(t===2&&(l=ps(e),l!==0&&(r=l,t=Vs(e,l))),t===1)throw n=qn,St(e,0),tt(e,r),he(e,K()),n;if(t===6)tt(e,r);else{if(l=e.current.alternate,!(r&30)&&!zf(l)&&(t=ll(e,r),t===2&&(i=ps(e),i!==0&&(r=i,t=Vs(e,i))),t===1))throw n=qn,St(e,0),tt(e,r),he(e,K()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(g(345));case 2:jt(e,de,$e);break;case 3:if(tt(e,r),(r&130023424)===r&&(t=zi+500-K(),10<t)){if(Ur(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){oe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=ws(jt.bind(null,e,de,$e),t);break}jt(e,de,$e);break;case 4:if(tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var a=31-Ie(r);i=1<<a,a=t[a],a>l&&(l=a),r&=~i}if(r=l,r=K()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Tf(r/1960))-r,10<r){e.timeoutHandle=ws(jt.bind(null,e,de,$e),r);break}jt(e,de,$e);break;case 5:jt(e,de,$e);break;default:throw Error(g(329))}}}return he(e,K()),e.callbackNode===n?lc.bind(null,e):null}function Vs(e,t){var n=Ln;return e.current.memoizedState.isDehydrated&&(St(e,t).flags|=256),e=ll(e,t),e!==2&&(t=de,de=n,t!==null&&Bs(t)),e}function Bs(e){de===null?de=e:de.push.apply(de,e)}function zf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!De(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function tt(e,t){for(t&=~Ti,t&=~vl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ie(t),r=1<<n;e[n]=-1,t&=~r}}function Ga(e){if(R&6)throw Error(g(327));Jt();var t=Ur(e,0);if(!(t&1))return he(e,K()),null;var n=ll(e,t);if(e.tag!==0&&n===2){var r=ps(e);r!==0&&(t=r,n=Vs(e,r))}if(n===1)throw n=qn,St(e,0),tt(e,t),he(e,K()),n;if(n===6)throw Error(g(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jt(e,de,$e),he(e,K()),null}function Ri(e,t){var n=R;R|=1;try{return e(t)}finally{R=n,R===0&&(on=K()+500,ml&&xt())}}function Tt(e){rt!==null&&rt.tag===0&&!(R&6)&&Jt();var t=R;R|=1;var n=Ee.transition,r=L;try{if(Ee.transition=null,L=1,e)return e()}finally{L=r,Ee.transition=n,R=t,!(R&6)&&xt()}}function Ii(){xe=Kt.current,O(Kt)}function St(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,af(n)),Y!==null)for(n=Y.return;n!==null;){var r=n;switch(mi(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Wr();break;case 3:sn(),O(me),O(ie),wi();break;case 5:Ni(r);break;case 4:sn();break;case 13:O(U);break;case 19:O(U);break;case 10:vi(r.type._context);break;case 22:case 23:Ii()}n=n.return}if(J=e,Y=e=dt(e.current,null),te=xe=t,X=0,qn=null,Ti=vl=Mt=0,de=Ln=null,wt!==null){for(t=0;t<wt.length;t++)if(n=wt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=l,r.next=a}n.pending=r}wt=null}return e}function sc(e,t){do{var n=Y;try{if(xi(),Mr.current=el,Jr){for(var r=$.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}Jr=!1}if(Pt=0,q=G=$=null,Rn=!1,Gn=0,Mi.current=null,n===null||n.return===null){X=1,qn=t,Y=null;break}e:{var i=e,a=n.return,o=n,u=t;if(t=te,o.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var d=u,x=o,h=x.tag;if(!(x.mode&1)&&(h===0||h===11||h===15)){var p=x.alternate;p?(x.updateQueue=p.updateQueue,x.memoizedState=p.memoizedState,x.lanes=p.lanes):(x.updateQueue=null,x.memoizedState=null)}var y=ba(a);if(y!==null){y.flags&=-257,Oa(y,a,o,i,t),y.mode&1&&Da(i,d,t),t=y,u=d;var j=t.updateQueue;if(j===null){var N=new Set;N.add(u),t.updateQueue=N}else j.add(u);break e}else{if(!(t&1)){Da(i,d,t),Li();break e}u=Error(g(426))}}else if(A&&o.mode&1){var F=ba(a);if(F!==null){!(F.flags&65536)&&(F.flags|=256),Oa(F,a,o,i,t),pi(an(u,o));break e}}i=u=an(u,o),X!==4&&(X=2),Ln===null?Ln=[i]:Ln.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var f=Vu(i,u,t);Ma(i,f);break e;case 1:o=u;var c=i.type,m=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(ut===null||!ut.has(m)))){i.flags|=65536,t&=-t,i.lanes|=t;var v=Bu(i,o,t);Ma(i,v);break e}}i=i.return}while(i!==null)}oc(n)}catch(k){t=k,Y===n&&n!==null&&(Y=n=n.return);continue}break}while(!0)}function ic(){var e=tl.current;return tl.current=el,e===null?el:e}function Li(){(X===0||X===3||X===2)&&(X=4),J===null||!(Mt&268435455)&&!(vl&268435455)||tt(J,te)}function ll(e,t){var n=R;R|=2;var r=ic();(J!==e||te!==t)&&($e=null,St(e,t));do try{Rf();break}catch(l){sc(e,l)}while(!0);if(xi(),R=n,tl.current=r,Y!==null)throw Error(g(261));return J=null,te=0,X}function Rf(){for(;Y!==null;)ac(Y)}function If(){for(;Y!==null&&!rd();)ac(Y)}function ac(e){var t=cc(e.alternate,e,xe);e.memoizedProps=e.pendingProps,t===null?oc(e):Y=t,Mi.current=null}function oc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Cf(n,t),n!==null){n.flags&=32767,Y=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{X=6,Y=null;return}}else if(n=Ef(n,t,xe),n!==null){Y=n;return}if(t=t.sibling,t!==null){Y=t;return}Y=t=e}while(t!==null);X===0&&(X=5)}function jt(e,t,n){var r=L,l=Ee.transition;try{Ee.transition=null,L=1,Lf(e,t,n,r)}finally{Ee.transition=l,L=r}return null}function Lf(e,t,n,r){do Jt();while(rt!==null);if(R&6)throw Error(g(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(g(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(md(e,i),e===J&&(Y=J=null,te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Nr||(Nr=!0,dc(Ar,function(){return Jt(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ee.transition,Ee.transition=null;var a=L;L=1;var o=R;R|=4,Mi.current=null,Pf(e,n),nc(n,e),Jd(js),$r=!!ys,js=ys=null,e.current=n,Mf(n),ld(),R=o,L=a,Ee.transition=i}else e.current=n;if(Nr&&(Nr=!1,rt=e,rl=l),i=e.pendingLanes,i===0&&(ut=null),ad(n.stateNode),he(e,K()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(nl)throw nl=!1,e=Us,Us=null,e;return rl&1&&e.tag!==0&&Jt(),i=e.pendingLanes,i&1?e===$s?Dn++:(Dn=0,$s=e):Dn=0,xt(),null}function Jt(){if(rt!==null){var e=Vo(rl),t=Ee.transition,n=L;try{if(Ee.transition=null,L=16>e?16:e,rt===null)var r=!1;else{if(e=rt,rt=null,rl=0,R&6)throw Error(g(331));var l=R;for(R|=4,w=e.current;w!==null;){var i=w,a=i.child;if(w.flags&16){var o=i.deletions;if(o!==null){for(var u=0;u<o.length;u++){var d=o[u];for(w=d;w!==null;){var x=w;switch(x.tag){case 0:case 11:case 15:In(8,x,i)}var h=x.child;if(h!==null)h.return=x,w=h;else for(;w!==null;){x=w;var p=x.sibling,y=x.return;if(Ju(x),x===d){w=null;break}if(p!==null){p.return=y,w=p;break}w=y}}}var j=i.alternate;if(j!==null){var N=j.child;if(N!==null){j.child=null;do{var F=N.sibling;N.sibling=null,N=F}while(N!==null)}}w=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,w=a;else e:for(;w!==null;){if(i=w,i.flags&2048)switch(i.tag){case 0:case 11:case 15:In(9,i,i.return)}var f=i.sibling;if(f!==null){f.return=i.return,w=f;break e}w=i.return}}var c=e.current;for(w=c;w!==null;){a=w;var m=a.child;if(a.subtreeFlags&2064&&m!==null)m.return=a,w=m;else e:for(a=c;w!==null;){if(o=w,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:xl(9,o)}}catch(k){B(o,o.return,k)}if(o===a){w=null;break e}var v=o.sibling;if(v!==null){v.return=o.return,w=v;break e}w=o.return}}if(R=l,xt(),Ae&&typeof Ae.onPostCommitFiberRoot=="function")try{Ae.onPostCommitFiberRoot(ol,e)}catch{}r=!0}return r}finally{L=n,Ee.transition=t}}return!1}function Xa(e,t,n){t=an(n,t),t=Vu(e,t,1),e=ot(e,t,1),t=oe(),e!==null&&(er(e,1,t),he(e,t))}function B(e,t,n){if(e.tag===3)Xa(e,e,n);else for(;t!==null;){if(t.tag===3){Xa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ut===null||!ut.has(r))){e=an(n,e),e=Bu(t,e,1),t=ot(t,e,1),e=oe(),t!==null&&(er(t,1,e),he(t,e));break}}t=t.return}}function Df(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=oe(),e.pingedLanes|=e.suspendedLanes&n,J===e&&(te&n)===n&&(X===4||X===3&&(te&130023424)===te&&500>K()-zi?St(e,0):Ti|=n),he(e,t)}function uc(e,t){t===0&&(e.mode&1?(t=dr,dr<<=1,!(dr&130023424)&&(dr=4194304)):t=1);var n=oe();e=Ye(e,t),e!==null&&(er(e,t,n),he(e,n))}function bf(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),uc(e,n)}function Of(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(g(314))}r!==null&&r.delete(t),uc(e,n)}var cc;cc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||me.current)fe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return fe=!1,Sf(e,t,n);fe=!!(e.flags&131072)}else fe=!1,A&&t.flags&1048576&&pu(t,Yr,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;zr(e,t),e=t.pendingProps;var l=nn(t,ie.current);qt(t,n),l=Si(null,t,r,e,l,n);var i=Ei();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,pe(r)?(i=!0,Qr(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,yi(t),l.updater=hl,t.stateNode=l,l._reactInternals=t,Ms(t,r,e,n),t=Rs(null,t,r,!0,i,n)):(t.tag=0,A&&i&&fi(t),ae(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(zr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=Af(r),e=Te(r,e),l){case 0:t=zs(null,t,r,e,n);break e;case 1:t=Ua(null,t,r,e,n);break e;case 11:t=Fa(null,t,r,e,n);break e;case 14:t=Aa(null,t,r,Te(r.type,e),n);break e}throw Error(g(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),zs(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),Ua(e,t,r,l,n);case 3:e:{if(Ku(t),e===null)throw Error(g(387));r=t.pendingProps,i=t.memoizedState,l=i.element,ju(e,t),Zr(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=an(Error(g(423)),t),t=$a(e,t,r,n,l);break e}else if(r!==l){l=an(Error(g(424)),t),t=$a(e,t,r,n,l);break e}else for(ve=at(t.stateNode.containerInfo.firstChild),ge=t,A=!0,Re=null,n=gu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(rn(),r===l){t=Ge(e,t,n);break e}ae(e,t,r,n)}t=t.child}return t;case 5:return Nu(t),e===null&&Cs(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,a=l.children,Ns(r,l)?a=null:i!==null&&Ns(r,i)&&(t.flags|=32),Qu(e,t),ae(e,t,a,n),t.child;case 6:return e===null&&Cs(t),null;case 13:return Yu(e,t,n);case 4:return ji(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=ln(t,null,r,n):ae(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),Fa(e,t,r,l,n);case 7:return ae(e,t,t.pendingProps,n),t.child;case 8:return ae(e,t,t.pendingProps.children,n),t.child;case 12:return ae(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,a=l.value,D(Gr,r._currentValue),r._currentValue=a,i!==null)if(De(i.value,a)){if(i.children===l.children&&!me.current){t=Ge(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var o=i.dependencies;if(o!==null){a=i.child;for(var u=o.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=We(-1,n&-n),u.tag=2;var d=i.updateQueue;if(d!==null){d=d.shared;var x=d.pending;x===null?u.next=u:(u.next=x.next,x.next=u),d.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),_s(i.return,n,t),o.lanes|=n;break}u=u.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(g(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),_s(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}ae(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,qt(t,n),l=Ce(l),r=r(l),t.flags|=1,ae(e,t,r,n),t.child;case 14:return r=t.type,l=Te(r,t.pendingProps),l=Te(r.type,l),Aa(e,t,r,l,n);case 15:return Hu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),zr(e,t),t.tag=1,pe(r)?(e=!0,Qr(t)):e=!1,qt(t,n),$u(t,r,l),Ms(t,r,l,n),Rs(null,t,r,!0,e,n);case 19:return Gu(e,t,n);case 22:return Wu(e,t,n)}throw Error(g(156,t.tag))};function dc(e,t){return Fo(e,t)}function Ff(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Se(e,t,n,r){return new Ff(e,t,n,r)}function Di(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Af(e){if(typeof e=="function")return Di(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ei)return 11;if(e===ti)return 14}return 2}function dt(e,t){var n=e.alternate;return n===null?(n=Se(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Lr(e,t,n,r,l,i){var a=2;if(r=e,typeof e=="function")Di(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Ot:return Et(n.children,l,i,t);case Js:a=8,l|=8;break;case Jl:return e=Se(12,n,t,l|2),e.elementType=Jl,e.lanes=i,e;case es:return e=Se(13,n,t,l),e.elementType=es,e.lanes=i,e;case ts:return e=Se(19,n,t,l),e.elementType=ts,e.lanes=i,e;case No:return gl(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case yo:a=10;break e;case jo:a=9;break e;case ei:a=11;break e;case ti:a=14;break e;case qe:a=16,r=null;break e}throw Error(g(130,e==null?e:typeof e,""))}return t=Se(a,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function Et(e,t,n,r){return e=Se(7,e,r,t),e.lanes=n,e}function gl(e,t,n,r){return e=Se(22,e,r,t),e.elementType=No,e.lanes=n,e.stateNode={isHidden:!1},e}function Xl(e,t,n){return e=Se(6,e,null,t),e.lanes=n,e}function Zl(e,t,n){return t=Se(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uf(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=zl(0),this.expirationTimes=zl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=zl(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function bi(e,t,n,r,l,i,a,o,u){return e=new Uf(e,t,n,o,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Se(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},yi(i),e}function $f(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:bt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function fc(e){if(!e)return mt;e=e._reactInternals;e:{if(It(e)!==e||e.tag!==1)throw Error(g(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(g(171))}if(e.tag===1){var n=e.type;if(pe(n))return fu(e,n,t)}return t}function mc(e,t,n,r,l,i,a,o,u){return e=bi(n,r,!0,e,l,i,a,o,u),e.context=fc(null),n=e.current,r=oe(),l=ct(n),i=We(r,l),i.callback=t??null,ot(n,i,l),e.current.lanes=l,er(e,l,r),he(e,r),e}function yl(e,t,n,r){var l=t.current,i=oe(),a=ct(l);return n=fc(n),t.context===null?t.context=n:t.pendingContext=n,t=We(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ot(l,t,a),e!==null&&(Le(e,l,a,i),Pr(e,l,a)),a}function sl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Za(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Oi(e,t){Za(e,t),(e=e.alternate)&&Za(e,t)}function Vf(){return null}var pc=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fi(e){this._internalRoot=e}jl.prototype.render=Fi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(g(409));yl(e,t,null,null)};jl.prototype.unmount=Fi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Tt(function(){yl(null,e,null,null)}),t[Ke]=null}};function jl(e){this._internalRoot=e}jl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Wo();e={blockedOn:null,target:e,priority:t};for(var n=0;n<et.length&&t!==0&&t<et[n].priority;n++);et.splice(n,0,e),n===0&&Ko(e)}};function Ai(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Nl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function qa(){}function Bf(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var d=sl(a);i.call(d)}}var a=mc(t,r,e,0,null,!1,!1,"",qa);return e._reactRootContainer=a,e[Ke]=a.current,Hn(e.nodeType===8?e.parentNode:e),Tt(),a}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var o=r;r=function(){var d=sl(u);o.call(d)}}var u=bi(e,0,!1,null,null,!1,!1,"",qa);return e._reactRootContainer=u,e[Ke]=u.current,Hn(e.nodeType===8?e.parentNode:e),Tt(function(){yl(t,u,n,r)}),u}function wl(e,t,n,r,l){var i=n._reactRootContainer;if(i){var a=i;if(typeof l=="function"){var o=l;l=function(){var u=sl(a);o.call(u)}}yl(t,a,e,l)}else a=Bf(n,t,e,l,r);return sl(a)}Bo=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Sn(t.pendingLanes);n!==0&&(li(t,n|1),he(t,K()),!(R&6)&&(on=K()+500,xt()))}break;case 13:Tt(function(){var r=Ye(e,1);if(r!==null){var l=oe();Le(r,e,1,l)}}),Oi(e,1)}};si=function(e){if(e.tag===13){var t=Ye(e,134217728);if(t!==null){var n=oe();Le(t,e,134217728,n)}Oi(e,134217728)}};Ho=function(e){if(e.tag===13){var t=ct(e),n=Ye(e,t);if(n!==null){var r=oe();Le(n,e,t,r)}Oi(e,t)}};Wo=function(){return L};Qo=function(e,t){var n=L;try{return L=e,t()}finally{L=n}};ds=function(e,t,n){switch(t){case"input":if(ls(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=fl(r);if(!l)throw Error(g(90));ko(r),ls(r,l)}}}break;case"textarea":Eo(e,n);break;case"select":t=n.value,t!=null&&Yt(e,!!n.multiple,t,!1)}};Ro=Ri;Io=Tt;var Hf={usingClientEntryPoint:!1,Events:[nr,$t,fl,To,zo,Ri]},Nn={findFiberByHostInstance:Nt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Wf={bundleType:Nn.bundleType,version:Nn.version,rendererPackageName:Nn.rendererPackageName,rendererConfig:Nn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Xe.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=bo(e),e===null?null:e.stateNode},findFiberByHostInstance:Nn.findFiberByHostInstance||Vf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wr.isDisabled&&wr.supportsFiber)try{ol=wr.inject(Wf),Ae=wr}catch{}}je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hf;je.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ai(t))throw Error(g(200));return $f(e,t,null,n)};je.createRoot=function(e,t){if(!Ai(e))throw Error(g(299));var n=!1,r="",l=pc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=bi(e,1,!1,null,null,n,!1,r,l),e[Ke]=t.current,Hn(e.nodeType===8?e.parentNode:e),new Fi(t)};je.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(g(188)):(e=Object.keys(e).join(","),Error(g(268,e)));return e=bo(t),e=e===null?null:e.stateNode,e};je.flushSync=function(e){return Tt(e)};je.hydrate=function(e,t,n){if(!Nl(t))throw Error(g(200));return wl(null,e,t,!0,n)};je.hydrateRoot=function(e,t,n){if(!Ai(e))throw Error(g(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",a=pc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=mc(t,null,e,1,n??null,l,!1,i,a),e[Ke]=t.current,Hn(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new jl(t)};je.render=function(e,t,n){if(!Nl(t))throw Error(g(200));return wl(null,e,t,!1,n)};je.unmountComponentAtNode=function(e){if(!Nl(e))throw Error(g(40));return e._reactRootContainer?(Tt(function(){wl(null,null,e,!1,function(){e._reactRootContainer=null,e[Ke]=null})}),!0):!1};je.unstable_batchedUpdates=Ri;je.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Nl(n))throw Error(g(200));if(e==null||e._reactInternals===void 0)throw Error(g(38));return wl(e,t,n,!1,r)};je.version="18.3.1-next-f1338f8080-20240426";function hc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(hc)}catch(e){console.error(e)}}hc(),ho.exports=je;var Qf=ho.exports,xc,Ja=Qf;xc=Ja.createRoot,Ja.hydrateRoot;/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Kf={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),I=(e,t)=>{const n=H.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:o="",children:u,...d},x)=>H.createElement("svg",{ref:x,...Kf,width:l,height:l,stroke:r,strokeWidth:a?Number(i)*24/Number(l):i,className:["lucide",`lucide-${Yf(e)}`,o].join(" "),...d},[...t.map(([h,p])=>H.createElement(h,p)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=I("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hs=I("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gf=I("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xf=I("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vc=I("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zt=I("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ui=I("CheckCircle2",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dt=I("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zf=I("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $i=I("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=I("FileImage",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=I("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jf=I("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const em=I("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const en=I("Layers3",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m6.08 9.5-3.5 1.6a1 1 0 0 0 0 1.81l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9a1 1 0 0 0 0-1.83l-3.5-1.59",key:"1e5n1m"}],["path",{d:"m6.08 14.5-3.5 1.6a1 1 0 0 0 0 1.81l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9a1 1 0 0 0 0-1.83l-3.5-1.59",key:"1iwflc"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vi=I("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tm=I("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nm=I("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bi=I("Microscope",[["path",{d:"M6 18h8",key:"1borvv"}],["path",{d:"M3 22h18",key:"8prr45"}],["path",{d:"M14 22a7 7 0 1 0 0-14h-1",key:"1jwaiy"}],["path",{d:"M9 14h2",key:"197e7h"}],["path",{d:"M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z",key:"1bmzmy"}],["path",{d:"M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3",key:"1drr47"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rm=I("Move3d",[["path",{d:"M5 3v16h16",key:"1mqmf9"}],["path",{d:"m5 19 6-6",key:"jh6hbb"}],["path",{d:"m2 6 3-3 3 3",key:"tkyvxa"}],["path",{d:"m18 16 3 3-3 3",key:"1d4glt"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const il=I("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lm=I("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const to=I("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cn=I("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=I("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gc=I("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sm=I("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const im=I("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const am=I("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const om=I("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const um=I("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cm=I("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),dm=()=>s.jsx("header",{className:"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-slate-200 shadow-sm",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex items-center justify-between h-20",children:[s.jsx("div",{className:"flex items-center space-x-4",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"p-2 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl shadow-lg",children:s.jsx(zt,{className:"w-8 h-8 text-white"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-xl font-bold text-slate-900 leading-tight",children:"Automated Brain Tumor Analysis"}),s.jsx("p",{className:"text-sm text-slate-600 font-medium",children:"3D Reconstruction from MRI"})]})]})}),s.jsxs("div",{className:"hidden md:flex items-center space-x-6",children:[s.jsxs("div",{className:"flex items-center space-x-4 px-4 py-2 bg-slate-50 rounded-lg border border-slate-200",children:[s.jsx(sm,{className:"w-4 h-4 text-slate-600"}),s.jsxs("div",{className:"text-sm",children:[s.jsx("div",{className:"font-semibold text-slate-900",children:"Dr. Mohammed Yagoub Esmail"}),s.jsx("div",{className:"text-slate-600",children:"SUST - BME @ 2025"})]})]}),s.jsxs("div",{className:"flex items-center space-x-3 text-sm text-slate-600",children:[s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(il,{className:"w-4 h-4"}),s.jsx("span",{children:"+249912867327"})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Vi,{className:"w-4 h-4"}),s.jsx("span",{className:"hidden lg:inline",children:"<EMAIL>"})]})]})]})]})})}),fm=({activeSection:e,onSectionChange:t,mobileMenuOpen:n,onMobileMenuToggle:r})=>{const l=[{id:"upload",label:"Upload MRI",icon:gc},{id:"analysis",label:"Analysis",icon:Dr},{id:"3d",label:"3D Reconstruction",icon:en},{id:"explainable",label:"Explainable AI",icon:Bi},{id:"research",label:"Research Info",icon:vc}];return s.jsx("nav",{className:"fixed top-20 left-0 right-0 z-40 bg-white/90 backdrop-blur-sm border-b border-slate-200",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"flex items-center justify-between h-12",children:[s.jsx("div",{className:"hidden md:flex space-x-1",children:l.map(i=>{const a=i.icon,o=e===i.id;return s.jsxs("button",{onClick:()=>t(i.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${o?"bg-blue-100 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"}`,children:[s.jsx(a,{className:"w-4 h-4"}),s.jsx("span",{children:i.label})]},i.id)})}),s.jsx("div",{className:"md:hidden",children:s.jsx("button",{onClick:r,className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg",children:n?s.jsx(am,{className:"w-5 h-5"}):s.jsx(nm,{className:"w-5 h-5"})})})]}),n&&s.jsx("div",{className:"md:hidden py-4 border-t border-slate-200",children:s.jsx("div",{className:"space-y-2",children:l.map(i=>{const a=i.icon,o=e===i.id;return s.jsxs("button",{onClick:()=>{t(i.id),r()},className:`flex items-center space-x-3 w-full px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${o?"bg-blue-100 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"}`,children:[s.jsx(a,{className:"w-4 h-4"}),s.jsx("span",{children:i.label})]},i.id)})})})]})})},no=({onAnalysisStart:e})=>{const[t,n]=H.useState(!1),[r,l]=H.useState([]),[i,a]=H.useState(!1),o=H.useCallback(p=>{p.preventDefault(),p.stopPropagation(),p.type==="dragenter"||p.type==="dragover"?n(!0):p.type==="dragleave"&&n(!1)},[]),u=H.useCallback(p=>{if(p.preventDefault(),p.stopPropagation(),n(!1),p.dataTransfer.files&&p.dataTransfer.files[0]){const y=Array.from(p.dataTransfer.files).filter(j=>j.type.startsWith("image/")||j.name.endsWith(".dcm"));l(j=>[...j,...y])}},[]),d=p=>{if(p.target.files){const y=Array.from(p.target.files);l(j=>[...j,...y])}},x=()=>{a(!0),setTimeout(()=>{a(!1),e()},2e3)},h=p=>{if(p===0)return"0 Bytes";const y=1024,j=["Bytes","KB","MB","GB"],N=Math.floor(Math.log(p)/Math.log(y));return parseFloat((p/Math.pow(y,N)).toFixed(2))+" "+j[N]};return s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"p-4 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl shadow-lg",children:s.jsx(zt,{className:"w-12 h-12 text-white"})})}),s.jsx("h2",{className:"text-3xl font-bold text-slate-900 mb-3",children:"Upload MRI Scan for Analysis"}),s.jsx("p",{className:"text-lg text-slate-600 max-w-2xl mx-auto",children:"Upload your MRI DICOM files or medical images for automated brain tumor detection, segmentation, and 3D reconstruction using advanced deep learning algorithms."})]}),s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsx("div",{onDragEnter:o,onDragLeave:o,onDragOver:o,onDrop:u,className:`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${t?"border-blue-500 bg-blue-50":"border-slate-300 bg-white hover:border-blue-400 hover:bg-blue-50/50"}`,children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:"flex justify-center",children:s.jsx("div",{className:`p-6 rounded-2xl transition-colors duration-300 ${t?"bg-blue-100":"bg-slate-100"}`,children:s.jsx(gc,{className:`w-12 h-12 ${t?"text-blue-600":"text-slate-500"}`})})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-slate-900 mb-2",children:"Drop your MRI files here"}),s.jsx("p",{className:"text-slate-600 mb-4",children:"Supports DICOM (.dcm), NIfTI (.nii), and common image formats (JPEG, PNG, TIFF)"}),s.jsxs("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[s.jsxs("span",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl",children:[s.jsx(eo,{className:"w-5 h-5 mr-2"}),"Browse Files"]}),s.jsx("input",{id:"file-upload",type:"file",multiple:!0,accept:".dcm,.nii,.nii.gz,.jpg,.jpeg,.png,.tiff,.tif",onChange:d,className:"hidden"})]})]}),s.jsxs("div",{className:"flex flex-wrap justify-center gap-4 text-sm text-slate-500",children:[s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsx("span",{children:"DICOM Compatible"})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsx("span",{children:"3D Reconstruction Ready"})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),s.jsx("span",{children:"AI-Powered Analysis"})]})]})]})}),r.length>0&&s.jsxs("div",{className:"mt-8 bg-white rounded-xl border border-slate-200 shadow-sm",children:[s.jsx("div",{className:"px-6 py-4 border-b border-slate-200",children:s.jsxs("h4",{className:"text-lg font-semibold text-slate-900 flex items-center",children:[s.jsx(Ui,{className:"w-5 h-5 text-green-600 mr-2"}),"Uploaded Files (",r.length,")"]})}),s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"space-y-3",children:r.map((p,y)=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(eo,{className:"w-8 h-8 text-blue-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-slate-900",children:p.name}),s.jsx("div",{className:"text-sm text-slate-500",children:h(p.size)})]})]}),s.jsx("div",{className:"text-sm text-green-600 font-medium",children:"Ready"})]},y))}),s.jsx("div",{className:"mt-6 pt-6 border-t border-slate-200",children:s.jsx("button",{onClick:x,disabled:i,className:"w-full flex items-center justify-center px-6 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed",children:i?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-3"}),"Processing MRI Data..."]}):s.jsxs(s.Fragment,{children:[s.jsx(om,{className:"w-5 h-5 mr-2"}),"Start AI Analysis"]})})})]})]}),s.jsxs("div",{className:"mt-12 grid md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-white rounded-xl p-6 border border-slate-200 shadow-sm hover:shadow-md transition-shadow",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:s.jsx(zt,{className:"w-6 h-6 text-blue-600"})}),s.jsx("h5",{className:"font-semibold text-slate-900",children:"U-Net Segmentation"})]}),s.jsx("p",{className:"text-slate-600 text-sm",children:"Advanced deep learning model for precise brain tumor boundary detection and segmentation."})]}),s.jsxs("div",{className:"bg-white rounded-xl p-6 border border-slate-200 shadow-sm hover:shadow-md transition-shadow",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:s.jsx(Hs,{className:"w-6 h-6 text-green-600"})}),s.jsx("h5",{className:"font-semibold text-slate-900",children:"CAD System"})]}),s.jsx("p",{className:"text-slate-600 text-sm",children:"Computer-Aided Diagnosis providing detailed analysis and clinical insights."})]}),s.jsxs("div",{className:"bg-white rounded-xl p-6 border border-slate-200 shadow-sm hover:shadow-md transition-shadow",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:s.jsx(Bi,{className:"w-6 h-6 text-purple-600"})}),s.jsx("h5",{className:"font-semibold text-slate-900",children:"Explainable AI"})]}),s.jsx("p",{className:"text-slate-600 text-sm",children:"Transparent AI decision-making with visual explanations for clinical validation."})]})]})]})]})},mm=({on3DView:e})=>{const[t,n]=H.useState(0),[r,l]=H.useState("Preprocessing"),[i,a]=H.useState(!1),o=[{name:"Preprocessing",duration:1e3},{name:"U-Net Segmentation",duration:2e3},{name:"Tumor Detection",duration:1500},{name:"Feature Extraction",duration:1e3},{name:"Classification",duration:800},{name:"Report Generation",duration:700}];return H.useEffect(()=>{let u=0,d=0;const x=()=>{if(u<o.length){l(o[u].name);const h=100/o.length;o[u].duration/h;const p=setInterval(()=>{d+=100/o.length/(o[u].duration/50),n(Math.min(d,(u+1)*(100/o.length))),d>=(u+1)*(100/o.length)&&(clearInterval(p),u++,setTimeout(x,200))},50)}else a(!0),l("Analysis Complete")};x()},[]),s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"p-4 bg-gradient-to-br from-green-600 to-emerald-700 rounded-2xl shadow-lg",children:s.jsx(Dr,{className:"w-12 h-12 text-white"})})}),s.jsx("h2",{className:"text-3xl font-bold text-slate-900 mb-3",children:"Brain Tumor Analysis Results"}),s.jsx("p",{className:"text-lg text-slate-600",children:"AI-powered analysis using U-Net deep learning architecture"})]}),!i&&s.jsxs("div",{className:"max-w-3xl mx-auto bg-white rounded-xl border border-slate-200 shadow-lg p-8",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsx("h3",{className:"text-xl font-semibold text-slate-900 mb-2",children:"Processing MRI Scan"}),s.jsxs("p",{className:"text-slate-600",children:["Current Phase: ",r]})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-full bg-slate-200 rounded-full h-3",children:s.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-green-600 h-3 rounded-full transition-all duration-300 ease-out",style:{width:`${t}%`}})}),s.jsxs("div",{className:"mt-2 text-center text-slate-600 font-medium",children:[Math.round(t),"% Complete"]})]}),s.jsx("div",{className:"mt-8 grid grid-cols-2 md:grid-cols-3 gap-4",children:o.map((u,d)=>s.jsx("div",{className:`p-3 rounded-lg border text-sm text-center transition-all duration-300 ${d<t/(100/o.length)?"bg-green-50 border-green-200 text-green-800":d===Math.floor(t/(100/o.length))?"bg-blue-50 border-blue-200 text-blue-800 animate-pulse":"bg-slate-50 border-slate-200 text-slate-600"}`,children:u.name},u.name))})]}),i&&s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",children:s.jsxs("h3",{className:"text-xl font-semibold flex items-center",children:[s.jsx(Cn,{className:"w-6 h-6 mr-2"}),"Key Findings"]})}),s.jsx("div",{className:"p-6",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Gf,{className:"w-6 h-6 text-red-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-red-900",children:"Tumor Detected"}),s.jsx("div",{className:"text-sm text-red-700",children:"High Confidence"})]})]}),s.jsx("div",{className:"text-2xl font-bold text-red-600",children:"94%"})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 bg-orange-50 border border-orange-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(zt,{className:"w-6 h-6 text-orange-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-orange-900",children:"Tumor Volume"}),s.jsx("div",{className:"text-sm text-orange-700",children:"Estimated Size"})]})]}),s.jsx("div",{className:"text-lg font-bold text-orange-600",children:"2.3 cm³"})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Cn,{className:"w-6 h-6 text-blue-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-blue-900",children:"Location"}),s.jsx("div",{className:"text-sm text-blue-700",children:"Primary Region"})]})]}),s.jsx("div",{className:"text-sm font-semibold text-blue-600",children:"Frontal Lobe"})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Ws,{className:"w-6 h-6 text-purple-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-purple-900",children:"Segmentation Accuracy"}),s.jsx("div",{className:"text-sm text-purple-700",children:"U-Net Performance"})]})]}),s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:"91.7%"})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Ui,{className:"w-6 h-6 text-green-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-green-900",children:"Processing Status"}),s.jsx("div",{className:"text-sm text-green-700",children:"Analysis Complete"})]})]}),s.jsx("div",{className:"text-sm font-semibold text-green-600",children:"Success"})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 bg-indigo-50 border border-indigo-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Dr,{className:"w-6 h-6 text-indigo-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-indigo-900",children:"Processing Time"}),s.jsx("div",{className:"text-sm text-indigo-700",children:"Total Duration"})]})]}),s.jsx("div",{className:"text-sm font-semibold text-indigo-600",children:"7.2s"})]})]})]})})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white",children:s.jsxs("h3",{className:"text-xl font-semibold flex items-center",children:[s.jsx(zt,{className:"w-6 h-6 mr-2"}),"U-Net Segmentation Results"]})}),s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-full h-48 bg-slate-100 rounded-lg border-2 border-dashed border-slate-300 flex items-center justify-center mb-3",children:s.jsxs("div",{className:"text-center",children:[s.jsx($i,{className:"w-12 h-12 text-slate-400 mx-auto mb-2"}),s.jsx("div",{className:"text-sm text-slate-600",children:"Original MRI"})]})}),s.jsx("h4",{className:"font-semibold text-slate-900",children:"Input Image"}),s.jsx("p",{className:"text-sm text-slate-600",children:"T1-weighted MRI"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-full h-48 bg-gradient-to-br from-red-100 to-orange-100 rounded-lg border-2 border-red-200 flex items-center justify-center mb-3",children:s.jsxs("div",{className:"text-center",children:[s.jsx(Cn,{className:"w-12 h-12 text-red-600 mx-auto mb-2"}),s.jsx("div",{className:"text-sm text-red-700",children:"Tumor Mask"})]})}),s.jsx("h4",{className:"font-semibold text-slate-900",children:"Segmentation Mask"}),s.jsx("p",{className:"text-sm text-slate-600",children:"U-Net Output"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-full h-48 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg border-2 border-blue-200 flex items-center justify-center mb-3",children:s.jsxs("div",{className:"text-center",children:[s.jsx(en,{className:"w-12 h-12 text-blue-600 mx-auto mb-2"}),s.jsx("div",{className:"text-sm text-blue-700",children:"3D Model"})]})}),s.jsx("h4",{className:"font-semibold text-slate-900",children:"3D Reconstruction"}),s.jsx("p",{className:"text-sm text-slate-600",children:"Marching Cubes"})]})]}),s.jsx("div",{className:"mt-8 flex justify-center",children:s.jsxs("button",{onClick:e,className:"flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl",children:[s.jsx(en,{className:"w-5 h-5 mr-2"}),"View 3D Reconstruction"]})})]})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("h4",{className:"text-lg font-semibold text-slate-900 mb-4 flex items-center",children:[s.jsx(Dr,{className:"w-5 h-5 mr-2 text-blue-600"}),"Model Performance"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Dice Score"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"0.887"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"IoU Score"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"0.823"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Sensitivity"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"94.2%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Specificity"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"98.1%"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("h4",{className:"text-lg font-semibold text-slate-900 mb-4 flex items-center",children:[s.jsx(Cn,{className:"w-5 h-5 mr-2 text-green-600"}),"Clinical Metrics"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Tumor Grade"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Grade II"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Enhancement Pattern"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Heterogeneous"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Edema Present"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Yes"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Necrosis"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Minimal"})]})]})]})]})]})]})},pm=()=>{const[e,t]=H.useState("3d"),[n,r]=H.useState(!0),[l,i]=H.useState({isoValue:.5,smoothing:2,transparency:.8,wireframe:!1});return s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"p-4 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-2xl shadow-lg",children:s.jsx(en,{className:"w-12 h-12 text-white"})})}),s.jsx("h2",{className:"text-3xl font-bold text-slate-900 mb-3",children:"3D Brain Tumor Reconstruction"}),s.jsx("p",{className:"text-lg text-slate-600",children:"Interactive 3D visualization using Marching Cubes algorithm"})]}),s.jsx("div",{className:"max-w-6xl mx-auto",children:s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsxs("div",{className:"px-6 py-4 bg-slate-50 border-b border-slate-200 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-slate-900",children:"3D Viewer"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("button",{onClick:()=>t("3d"),className:`px-3 py-1 text-sm rounded-lg transition-colors ${e==="3d"?"bg-blue-600 text-white":"bg-white text-slate-600 hover:bg-slate-100"}`,children:"3D View"}),s.jsx("button",{onClick:()=>t("slice"),className:`px-3 py-1 text-sm rounded-lg transition-colors ${e==="slice"?"bg-blue-600 text-white":"bg-white text-slate-600 hover:bg-slate-100"}`,children:"Slice View"})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("button",{className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors",children:s.jsx(lm,{className:"w-5 h-5"})}),s.jsx("button",{className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors",children:s.jsx(um,{className:"w-5 h-5"})}),s.jsx("button",{className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors",children:s.jsx(cm,{className:"w-5 h-5"})}),s.jsx("button",{className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors",children:s.jsx(rm,{className:"w-5 h-5"})}),s.jsx("button",{onClick:()=>r(!n),className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors",children:s.jsx(to,{className:"w-5 h-5"})}),s.jsx("button",{className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors",children:s.jsx(Dt,{className:"w-5 h-5"})})]})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"h-96 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center",children:s.jsxs("div",{className:"text-center text-white",children:[s.jsxs("div",{className:"relative mb-6",children:[s.jsxs("div",{className:"w-64 h-48 mx-auto relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full opacity-60 transform rotate-12"}),s.jsx("div",{className:"absolute top-8 left-16 w-20 h-16 bg-gradient-to-br from-red-500 to-red-700 rounded-full opacity-90 shadow-lg"}),s.jsx("div",{className:"absolute top-12 right-12 w-16 h-12 bg-gradient-to-br from-orange-400 to-red-600 rounded-full opacity-70"}),s.jsx("div",{className:"absolute bottom-16 left-20 w-12 h-10 bg-gradient-to-br from-red-600 to-red-800 rounded-full opacity-80"})]}),s.jsx("div",{className:"animate-spin-slow absolute inset-0 border-2 border-blue-400 border-dashed rounded-full opacity-30"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[s.jsx(en,{className:"w-6 h-6 text-blue-400"}),s.jsx("span",{className:"text-lg font-semibold",children:"3D Brain Model"})]}),s.jsx("div",{className:"text-sm text-slate-300",children:"Interactive visualization with tumor segmentation"}),s.jsx("div",{className:"text-xs text-slate-400",children:"Click and drag to rotate • Scroll to zoom"})]})]})}),n&&s.jsxs("div",{className:"absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg border border-slate-200 shadow-lg p-4 w-72",children:[s.jsx("h4",{className:"text-sm font-semibold text-slate-900 mb-3",children:"Reconstruction Settings"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"block text-xs font-medium text-slate-700 mb-1",children:["Iso Value: ",l.isoValue]}),s.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:l.isoValue,onChange:a=>i(o=>({...o,isoValue:parseFloat(a.target.value)})),className:"w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-xs font-medium text-slate-700 mb-1",children:["Smoothing: ",l.smoothing]}),s.jsx("input",{type:"range",min:"0",max:"5",step:"1",value:l.smoothing,onChange:a=>i(o=>({...o,smoothing:parseInt(a.target.value)})),className:"w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-xs font-medium text-slate-700 mb-1",children:["Transparency: ",l.transparency]}),s.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:l.transparency,onChange:a=>i(o=>({...o,transparency:parseFloat(a.target.value)})),className:"w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",id:"wireframe",checked:l.wireframe,onChange:a=>i(o=>({...o,wireframe:a.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),s.jsx("label",{htmlFor:"wireframe",className:"text-xs font-medium text-slate-700",children:"Wireframe Mode"})]})]})]})]})]})}),s.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:s.jsx(en,{className:"w-6 h-6 text-purple-600"})}),s.jsx("h4",{className:"text-lg font-semibold text-slate-900",children:"Marching Cubes"})]}),s.jsxs("div",{className:"space-y-3 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Algorithm"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Marching Cubes"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Grid Resolution"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"256³"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Vertices"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"45,782"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Triangles"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"91,564"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:s.jsx($i,{className:"w-6 h-6 text-red-600"})}),s.jsx("h4",{className:"text-lg font-semibold text-slate-900",children:"Tumor Properties"})]}),s.jsxs("div",{className:"space-y-3 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Volume"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"2.3 cm³"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Surface Area"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"8.7 cm²"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Max Diameter"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"18.4 mm"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Compactness"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"0.73"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:s.jsx(to,{className:"w-6 h-6 text-blue-600"})}),s.jsx("h4",{className:"text-lg font-semibold text-slate-900",children:"Render Settings"})]}),s.jsxs("div",{className:"space-y-3 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Quality"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"High"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Lighting"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Phong"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Anti-aliasing"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"4x MSAA"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Frame Rate"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"60 FPS"})]})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("h4",{className:"text-lg font-semibold text-slate-900 mb-4 flex items-center",children:[s.jsx(Dt,{className:"w-5 h-5 mr-2 text-green-600"}),"Export Options"]}),s.jsxs("div",{className:"grid md:grid-cols-4 gap-4",children:[s.jsxs("button",{className:"flex flex-col items-center p-4 border border-slate-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2",children:s.jsx(Dt,{className:"w-4 h-4 text-blue-600"})}),s.jsx("span",{className:"text-sm font-medium text-slate-900",children:"STL File"}),s.jsx("span",{className:"text-xs text-slate-600",children:"3D Printing"})]}),s.jsxs("button",{className:"flex flex-col items-center p-4 border border-slate-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors",children:[s.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2",children:s.jsx(Dt,{className:"w-4 h-4 text-green-600"})}),s.jsx("span",{className:"text-sm font-medium text-slate-900",children:"OBJ File"}),s.jsx("span",{className:"text-xs text-slate-600",children:"3D Software"})]}),s.jsxs("button",{className:"flex flex-col items-center p-4 border border-slate-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors",children:[s.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2",children:s.jsx(Dt,{className:"w-4 h-4 text-purple-600"})}),s.jsx("span",{className:"text-sm font-medium text-slate-900",children:"DICOM RT"}),s.jsx("span",{className:"text-xs text-slate-600",children:"Clinical Use"})]}),s.jsxs("button",{className:"flex flex-col items-center p-4 border border-slate-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors",children:[s.jsx("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mb-2",children:s.jsx(Dt,{className:"w-4 h-4 text-orange-600"})}),s.jsx("span",{className:"text-sm font-medium text-slate-900",children:"Images"}),s.jsx("span",{className:"text-xs text-slate-600",children:"PNG/JPG"})]})]})]})]})},hm=()=>{const[e,t]=H.useState("tumor"),[n,r]=H.useState(!0),l=[{id:"tumor",name:"Tumor Core",confidence:94.2,color:"red"},{id:"edema",name:"Peritumoral Edema",confidence:87.8,color:"orange"},{id:"necrosis",name:"Necrotic Tissue",confidence:76.3,color:"purple"},{id:"enhancement",name:"Enhancement",confidence:91.5,color:"blue"}],i=[{name:"Texture Homogeneity",importance:.34,description:"Uniformity of tissue texture patterns"},{name:"Signal Intensity",importance:.28,description:"MRI signal characteristics in T1/T2 sequences"},{name:"Boundary Sharpness",importance:.22,description:"Definition of tumor-brain interface"},{name:"Shape Irregularity",importance:.19,description:"Deviation from smooth, round morphology"},{name:"Enhancement Pattern",importance:.16,description:"Contrast agent uptake characteristics"},{name:"Edema Extension",importance:.14,description:"Spread of surrounding tissue changes"}];return s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"p-4 bg-gradient-to-br from-emerald-600 to-teal-700 rounded-2xl shadow-lg",children:s.jsx(Bi,{className:"w-12 h-12 text-white"})})}),s.jsx("h2",{className:"text-3xl font-bold text-slate-900 mb-3",children:"Explainable AI Analysis"}),s.jsx("p",{className:"text-lg text-slate-600",children:"Understanding how the AI model makes diagnostic decisions"})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white",children:s.jsxs("h3",{className:"text-xl font-semibold flex items-center",children:[s.jsx(zt,{className:"w-6 h-6 mr-2"}),"AI Decision Process"]})}),s.jsx("div",{className:"p-6",children:s.jsxs("div",{className:"grid md:grid-cols-4 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:"1"})}),s.jsx("h4",{className:"font-semibold text-slate-900 mb-2",children:"Feature Extraction"}),s.jsx("p",{className:"text-sm text-slate-600",children:"CNN layers extract relevant features from MRI slices"}),s.jsx("div",{className:"mt-3 text-xs text-blue-600 font-medium",children:"32,768 features detected"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("div",{className:"text-2xl font-bold text-green-600",children:"2"})}),s.jsx("h4",{className:"font-semibold text-slate-900 mb-2",children:"U-Net Segmentation"}),s.jsx("p",{className:"text-sm text-slate-600",children:"Encoder-decoder network segments tumor regions"}),s.jsx("div",{className:"mt-3 text-xs text-green-600 font-medium",children:"91.7% accuracy achieved"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:"3"})}),s.jsx("h4",{className:"font-semibold text-slate-900 mb-2",children:"Classification"}),s.jsx("p",{className:"text-sm text-slate-600",children:"ResNet classifier determines tumor characteristics"}),s.jsx("div",{className:"mt-3 text-xs text-purple-600 font-medium",children:"94.2% confidence"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("div",{className:"text-2xl font-bold text-orange-600",children:"4"})}),s.jsx("h4",{className:"font-semibold text-slate-900 mb-2",children:"Validation"}),s.jsx("p",{className:"text-sm text-slate-600",children:"Ensemble voting and uncertainty quantification"}),s.jsx("div",{className:"mt-3 text-xs text-orange-600 font-medium",children:"High reliability score"})]})]})})]}),s.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsxs("div",{className:"px-6 py-4 bg-slate-50 border-b border-slate-200 flex items-center justify-between",children:[s.jsxs("h3",{className:"text-lg font-semibold text-slate-900 flex items-center",children:[s.jsx($i,{className:"w-5 h-5 mr-2 text-blue-600"}),"Attention Heatmap"]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm text-slate-600",children:"Show Heatmap"}),s.jsx("input",{type:"checkbox",checked:n,onChange:a=>r(a.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"})]})]}),s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"relative",children:s.jsx("div",{className:"w-full h-64 bg-slate-100 rounded-lg border border-slate-200 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-48 h-48 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full opacity-80 mx-auto"}),n&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx("div",{className:"w-32 h-24 bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 rounded-full opacity-60"})})]}),s.jsx("div",{className:"mt-4 text-sm text-slate-600",children:n?"Model attention overlay active":"Original MRI slice"})]})})}),s.jsxs("div",{className:"mt-6",children:[s.jsx("h4",{className:"font-semibold text-slate-900 mb-3",children:"Region Analysis"}),s.jsx("div",{className:"space-y-2",children:l.map(a=>s.jsxs("button",{onClick:()=>t(a.id),className:`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${e===a.id?`bg-${a.color}-50 border-${a.color}-200`:"bg-slate-50 border-slate-200 hover:bg-slate-100"}`,children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:`w-3 h-3 bg-${a.color}-500 rounded-full`}),s.jsx("span",{className:"text-sm font-medium text-slate-900",children:a.name})]}),s.jsxs("span",{className:"text-sm font-semibold text-slate-700",children:[a.confidence,"%"]})]},a.id))})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-slate-50 border-b border-slate-200",children:s.jsxs("h3",{className:"text-lg font-semibold text-slate-900 flex items-center",children:[s.jsx(Ws,{className:"w-5 h-5 mr-2 text-green-600"}),"Feature Importance"]})}),s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"space-y-4",children:i.map((a,o)=>s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm font-medium text-slate-900",children:a.name}),s.jsxs("div",{className:"group relative",children:[s.jsx(em,{className:"w-4 h-4 text-slate-400 cursor-help"}),s.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-slate-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity z-10 w-48",children:a.description})]})]}),s.jsxs("span",{className:"text-sm font-semibold text-slate-700",children:[(a.importance*100).toFixed(1),"%"]})]}),s.jsx("div",{className:"w-full bg-slate-200 rounded-full h-2",children:s.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-500",style:{width:`${a.importance*100}%`}})})]},a.name))}),s.jsx("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Hs,{className:"w-5 h-5 text-blue-600 mt-0.5"}),s.jsxs("div",{className:"text-sm",children:[s.jsx("div",{className:"font-medium text-blue-900 mb-1",children:"Model Interpretation"}),s.jsx("div",{className:"text-blue-700",children:"The model shows high confidence in tumor detection based on texture heterogeneity and signal intensity patterns. The boundary analysis indicates irregular margins consistent with malignant characteristics."})]})]})})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",children:s.jsxs("h3",{className:"text-xl font-semibold flex items-center",children:[s.jsx(Cn,{className:"w-6 h-6 mr-2"}),"Model Validation & Trust Metrics"]})}),s.jsx("div",{className:"p-6",children:s.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-semibold text-slate-900 flex items-center",children:[s.jsx(Ui,{className:"w-5 h-5 mr-2 text-green-600"}),"Reliability Scores"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Prediction Confidence"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"94.2%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Model Uncertainty"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"±5.8%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Ensemble Agreement"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"92.7%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Cross-validation Score"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"89.4%"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-semibold text-slate-900 flex items-center",children:[s.jsx(Ws,{className:"w-5 h-5 mr-2 text-blue-600"}),"Clinical Validation"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Radiologist Agreement"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"87.3%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"False Positive Rate"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"1.9%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"False Negative Rate"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"5.8%"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Clinical Utility Score"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"91.2%"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-semibold text-slate-900 flex items-center",children:[s.jsx(Hs,{className:"w-5 h-5 mr-2 text-orange-600"}),"Limitations & Warnings"]}),s.jsxs("div",{className:"space-y-3 text-sm",children:[s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{className:"text-slate-600",children:"Model trained on T1-weighted MRI sequences primarily"})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{className:"text-slate-600",children:"Performance may vary with different scanner parameters"})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{className:"text-slate-600",children:"Requires clinical validation for final diagnosis"})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{className:"text-slate-600",children:"Not suitable for pediatric cases (training limitation)"})]})]})]})]})})]})]})},xm=()=>{const e=["Brain Tumor Segmentation","3D MRI","Systematic Review","Deep Learning","U-Net","3D Reconstruction","Computer-Aided Diagnosis (CAD)","Explainable AI (XAI)","Marching Cube Algorithm"],t=[{title:"Automated Brain Tumor Segmentation Using Deep Learning: A Comprehensive Review",journal:"Medical Image Analysis",year:"2025",doi:"10.1016/j.media.2025.102145",citations:23},{title:"3D Reconstruction of Brain Tumors from MRI Using Marching Cubes Algorithm",journal:"Computer Methods in Biomechanics",year:"2024",doi:"10.1080/10255842.2024.2381234",citations:18},{title:"Explainable AI in Medical Imaging: Application to Brain Tumor Analysis",journal:"Nature Machine Intelligence",year:"2024",doi:"10.1038/s42256-024-00891-2",citations:45}],n=[{phase:"Data Preprocessing",description:"Skull stripping, intensity normalization, and registration of MRI sequences",techniques:["N4 bias field correction","FSL BET","ANTs registration"]},{phase:"Segmentation",description:"U-Net architecture with attention mechanisms for precise tumor boundary detection",techniques:["Attention U-Net","Residual connections","Multi-scale feature fusion"]},{phase:"3D Reconstruction",description:"Marching Cubes algorithm implementation for surface generation",techniques:["Iso-surface extraction","Mesh smoothing","Topology preservation"]},{phase:"Validation",description:"Clinical validation with radiologist annotations and cross-dataset testing",techniques:["Dice coefficient","Hausdorff distance","Inter-observer agreement"]}];return s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"p-4 bg-gradient-to-br from-indigo-600 to-blue-700 rounded-2xl shadow-lg",children:s.jsx(vc,{className:"w-12 h-12 text-white"})})}),s.jsx("h2",{className:"text-3xl font-bold text-slate-900 mb-3",children:"Research Information"}),s.jsx("p",{className:"text-lg text-slate-600",children:"Scientific methodology and technical documentation"})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 text-white",children:s.jsx("h3",{className:"text-xl font-semibold",children:"Research Overview"})}),s.jsx("div",{className:"p-6",children:s.jsxs("div",{className:"prose max-w-none",children:[s.jsx("p",{className:"text-slate-600 mb-6",children:"This research presents an automated system for brain tumor analysis and 3D reconstruction from MRI scans, incorporating state-of-the-art deep learning techniques with explainable AI components. The system combines U-Net segmentation architecture with advanced 3D visualization using the Marching Cubes algorithm."}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-semibold text-slate-900 mb-4",children:"Research Objectives"}),s.jsxs("ul",{className:"space-y-2 text-slate-600",children:[s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Develop accurate brain tumor segmentation using deep learning"})]}),s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Implement 3D reconstruction for clinical visualization"})]}),s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Provide explainable AI for clinical decision support"})]}),s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Validate system performance against clinical standards"})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-semibold text-slate-900 mb-4",children:"Key Innovations"}),s.jsxs("ul",{className:"space-y-2 text-slate-600",children:[s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Attention-enhanced U-Net architecture"})]}),s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Real-time 3D reconstruction pipeline"})]}),s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Integrated explainability framework"})]}),s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),s.jsx("span",{children:"Cross-platform web-based interface"})]})]})]})]})]})})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-slate-900 mb-4 flex items-center",children:[s.jsx(qf,{className:"w-5 h-5 mr-2 text-blue-600"}),"Research Keywords"]}),s.jsx("div",{className:"flex flex-wrap gap-2",children:e.map((r,l)=>s.jsx("span",{className:"px-3 py-1 bg-blue-50 text-blue-700 text-sm font-medium rounded-full border border-blue-200",children:r},l))})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-slate-50 border-b border-slate-200",children:s.jsx("h3",{className:"text-xl font-semibold text-slate-900",children:"Research Methodology"})}),s.jsx("div",{className:"p-6",children:s.jsx("div",{className:"space-y-6",children:n.map((r,l)=>s.jsxs("div",{className:"border-l-4 border-blue-500 pl-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-sm font-bold text-blue-600",children:l+1})}),s.jsx("h4",{className:"text-lg font-semibold text-slate-900",children:r.phase})]}),s.jsx("p",{className:"text-slate-600 mb-3",children:r.description}),s.jsx("div",{className:"flex flex-wrap gap-2",children:r.techniques.map((i,a)=>s.jsx("span",{className:"px-2 py-1 bg-slate-100 text-slate-700 text-xs font-medium rounded border",children:i},a))})]},r.phase))})})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg overflow-hidden",children:[s.jsx("div",{className:"px-6 py-4 bg-slate-50 border-b border-slate-200",children:s.jsxs("h3",{className:"text-xl font-semibold text-slate-900 flex items-center",children:[s.jsx(Xf,{className:"w-6 h-6 mr-2 text-yellow-600"}),"Related Publications"]})}),s.jsx("div",{className:"p-6",children:s.jsx("div",{className:"space-y-6",children:t.map((r,l)=>s.jsxs("div",{className:"p-4 bg-slate-50 rounded-lg border border-slate-200",children:[s.jsx("h4",{className:"font-semibold text-slate-900 mb-2",children:r.title}),s.jsxs("div",{className:"flex items-center space-x-4 text-sm text-slate-600 mb-2",children:[s.jsx("span",{className:"font-medium",children:r.journal}),s.jsx("span",{children:"•"}),s.jsx("span",{children:r.year}),s.jsx("span",{children:"•"}),s.jsxs("span",{children:[r.citations," citations"]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("span",{className:"text-sm text-slate-600",children:["DOI: ",r.doi]}),s.jsxs("button",{className:"flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm font-medium",children:[s.jsx(Zf,{className:"w-4 h-4"}),s.jsx("span",{children:"View Paper"})]})]})]},l))})})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-slate-900 mb-4 flex items-center",children:[s.jsx(Jf,{className:"w-5 h-5 mr-2 text-slate-600"}),"Technical Details"]}),s.jsxs("div",{className:"space-y-3 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Framework"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"PyTorch 2.0"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Model Architecture"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"Attention U-Net"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Training Dataset"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"BraTS 2023"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Validation Method"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"5-fold CV"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Hardware"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"NVIDIA A100"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-600",children:"Training Time"}),s.jsx("span",{className:"font-semibold text-slate-900",children:"72 hours"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl border border-slate-200 shadow-lg p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-slate-900 mb-4 flex items-center",children:[s.jsx(im,{className:"w-5 h-5 mr-2 text-green-600"}),"Research Team"]}),s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{className:"p-3 bg-slate-50 rounded-lg",children:[s.jsx("div",{className:"font-semibold text-slate-900",children:"Dr. Mohammed Yagoub Esmail"}),s.jsx("div",{className:"text-sm text-slate-600 mb-2",children:"Principal Investigator"}),s.jsx("div",{className:"text-sm text-slate-600",children:"SUST - Biomedical Engineering"}),s.jsx("div",{className:"flex items-center space-x-4 mt-2",children:s.jsxs("div",{className:"flex items-center space-x-1 text-sm text-slate-600",children:[s.jsx(Vi,{className:"w-4 h-4"}),s.jsx("span",{children:"<EMAIL>"})]})}),s.jsx("div",{className:"flex items-center space-x-4 mt-1",children:s.jsxs("div",{className:"flex items-center space-x-1 text-sm text-slate-600",children:[s.jsx(il,{className:"w-4 h-4"}),s.jsx("span",{children:"+249912867327 / +966538076790"})]})})]})}),s.jsx("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:s.jsxs("div",{className:"text-sm text-blue-800",children:[s.jsx("div",{className:"font-semibold mb-1",children:"Copyright Notice"}),s.jsx("div",{children:"© 2025 Dr. Mohammed Yagoub Esmail, SUST-BME"}),s.jsx("div",{children:"All rights reserved."})]})})]})]})]})},vm=()=>s.jsx("footer",{className:"bg-slate-900 text-white",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[s.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"p-2 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl shadow-lg",children:s.jsx(zt,{className:"w-6 h-6 text-white"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-bold",children:"Brain Tumor Analysis"}),s.jsx("p",{className:"text-sm text-slate-300",children:"3D Reconstruction System"})]})]}),s.jsx("p",{className:"text-slate-300 text-sm",children:"Advanced AI-powered brain tumor detection and 3D visualization system for medical research and clinical applications."})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"text-lg font-semibold",children:"Research Information"}),s.jsxs("div",{className:"space-y-2 text-sm text-slate-300",children:[s.jsx("div",{children:"Principal Investigator: Dr. Mohammed Yagoub Esmail"}),s.jsx("div",{children:"Institution: SUST - Biomedical Engineering"}),s.jsx("div",{children:"Year: 2025"}),s.jsx("div",{children:"License: Research Use Only"})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"text-lg font-semibold",children:"Contact Information"}),s.jsxs("div",{className:"space-y-3 text-sm text-slate-300",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Vi,{className:"w-4 h-4 text-blue-400"}),s.jsx("span",{children:"<EMAIL>"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(il,{className:"w-4 h-4 text-green-400"}),s.jsx("span",{children:"+249912867327"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(il,{className:"w-4 h-4 text-green-400"}),s.jsx("span",{children:"+966538076790"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(tm,{className:"w-4 h-4 text-red-400"}),s.jsx("span",{children:"Sudan University of Science and Technology"})]})]})]})]}),s.jsx("div",{className:"mt-8 pt-8 border-t border-slate-800 text-center",children:s.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0",children:[s.jsx("div",{className:"text-sm text-slate-400",children:"© 2025 Dr. Mohammed Yagoub Esmail, SUST-BME. All rights reserved."}),s.jsxs("div",{className:"flex items-center space-x-6 text-sm text-slate-400",children:[s.jsx("span",{children:"Research Use Only"}),s.jsx("span",{children:"•"}),s.jsx("span",{children:"AI-Powered Medical Imaging"}),s.jsx("span",{children:"•"}),s.jsx("span",{children:"Deep Learning Applications"})]})]})})]})});function gm(){const[e,t]=H.useState("upload"),[n,r]=H.useState(!1),l=()=>{switch(e){case"upload":return s.jsx(no,{onAnalysisStart:()=>t("analysis")});case"analysis":return s.jsx(mm,{on3DView:()=>t("3d")});case"3d":return s.jsx(pm,{});case"explainable":return s.jsx(hm,{});case"research":return s.jsx(xm,{});default:return s.jsx(no,{onAnalysisStart:()=>t("analysis")})}};return s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50",children:[s.jsx(dm,{}),s.jsx(fm,{activeSection:e,onSectionChange:t,mobileMenuOpen:n,onMobileMenuToggle:()=>r(!n)}),s.jsx("main",{className:"pt-32 pb-16",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l()})}),s.jsx(vm,{})]})}xc(document.getElementById("root")).render(s.jsx(H.StrictMode,{children:s.jsx(gm,{})}));
