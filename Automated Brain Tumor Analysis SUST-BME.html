<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Brain Tumor Analysis | SUST-BME</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #0ea5e9 100%);
        }
        .neuro-card {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            border-radius: 15px;
            overflow: hidden;
        }
        .neuro-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .mri-viewer {
            background: linear-gradient(145deg, #f0f4ff, #e6f0ff);
            border-radius: 12px;
        }
        .tumor-highlight {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }
        .model-container {
            perspective: 1000px;
        }
        .3d-model {
            transform-style: preserve-3d;
            animation: rotate 20s infinite linear;
        }
        @keyframes rotate {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="gradient-bg text-white">
        <div class="container mx-auto px-4 py-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <h1 class="text-3xl md:text-4xl font-bold mb-2">Automated Brain Tumor Analysis</h1>
                    <h2 class="text-xl md:text-2xl">3D Reconstruction from MRI</h2>
                    <p class="mt-4 text-blue-100">Advanced Deep Learning for Medical Imaging</p>
                </div>
                <div class="bg-white/10 p-4 rounded-lg backdrop-blur-sm">
                    <img src="https://via.placeholder.com/150" alt="Brain MRI" class="rounded-lg border-2 border-white/30">
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="text-blue-900 font-bold text-xl">NeuroAI</div>
                <div class="hidden md:flex space-x-8">
                    <a href="#overview" class="text-blue-800 hover:text-blue-600 transition">Overview</a>
                    <a href="#technology" class="text-blue-800 hover:text-blue-600 transition">Technology</a>
                    <a href="#demo" class="text-blue-800 hover:text-blue-600 transition">Demo</a>
                    <a href="#publications" class="text-blue-800 hover:text-blue-600 transition">Publications</a>
                    <a href="#contact" class="text-blue-800 hover:text-blue-600 transition">Contact</a>
                </div>
                <button class="md:hidden text-blue-800">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Overview Section -->
        <section id="overview" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-blue-900 mb-4">Brain Tumor Segmentation & 3D Reconstruction</h2>
                <p class="text-gray-600 max-w-3xl mx-auto">Our advanced AI system provides automated detection, segmentation, and 3D visualization of brain tumors from MRI scans, supporting clinicians in diagnosis and treatment planning.</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="neuro-card bg-white p-6">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-brain text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-blue-900">Accurate Segmentation</h3>
                    <p class="text-gray-600">Deep learning models precisely identify tumor boundaries using advanced U-Net architectures.</p>
                </div>

                <div class="neuro-card bg-white p-6">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-cube text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-blue-900">3D Reconstruction</h3>
                    <p class="text-gray-600">Marching Cube algorithm transforms 2D MRI slices into interactive 3D models for better visualization.</p>
                </div>

                <div class="neuro-card bg-white p-6">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-chart-line text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-blue-900">Explainable AI</h3>
                    <p class="text-gray-600">XAI techniques provide insights into model decisions, increasing trust in automated diagnoses.</p>
                </div>
            </div>
        </section>

        <!-- Technology Section -->
        <section id="technology" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-blue-900 mb-12">Advanced Technology Stack</h2>
            
            <div class="grid md:grid-cols-2 gap-8 items-center mb-12">
                <div class="mri-viewer p-6">
                    <div class="relative h-64 md:h-80 bg-gray-200 rounded-lg overflow-hidden">
                        <div class="absolute inset-0 bg-[url('https://via.placeholder.com/500x300')] bg-cover bg-center"></div>
                        <div class="absolute top-4 left-4 bg-red-500/80 text-white px-3 py-1 rounded-full text-sm font-bold tumor-highlight">Tumor Area</div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-2xl font-semibold text-blue-800 mb-4">Deep Learning Architecture</h3>
                    <p class="text-gray-600 mb-4">Our system employs a modified 3D U-Net architecture specifically optimized for brain MRI segmentation, achieving state-of-the-art performance on benchmark datasets.</p>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">Multi-modal MRI processing (T1, T2, FLAIR, DWI)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">Attention mechanisms for improved focus on tumor regions</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">Dice coefficient > 0.92 on BraTS dataset</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div class="order-1 md:order-2">
                    <h3 class="text-2xl font-semibold text-blue-800 mb-4">3D Visualization Pipeline</h3>
                    <p class="text-gray-600 mb-4">The marching cube algorithm transforms segmented 2D slices into detailed 3D models, allowing clinicians to examine tumors from any angle and plan surgical interventions.</p>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-700 mb-2">Key Features:</h4>
                        <ul class="list-disc list-inside text-gray-700 space-y-1">
                            <li>Interactive 3D model rotation and zoom</li>
                            <li>Tumor volume calculation</li>
                            <li>Distance measurement tools</li>
                            <li>Virtual resection planning</li>
                        </ul>
                    </div>
                </div>
                
                <div class="model-container order-2 md:order-1 flex justify-center">
                    <div class="3d-model w-64 h-64 bg-gradient-to-br from-blue-100 to-blue-300 rounded-full shadow-xl flex items-center justify-center">
                        <i class="fas fa-brain text-6xl text-blue-700 opacity-70"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Demo Section -->
        <section id="demo" class="mb-16 bg-blue-50 rounded-xl p-8">
            <h2 class="text-3xl font-bold text-center text-blue-900 mb-8">Interactive Demo</h2>
            
            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-4 rounded-lg shadow">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Upload MRI Scan</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <i class="fas fa-cloud-upload-alt text-3xl text-blue-500 mb-2"></i>
                        <p class="text-sm text-gray-500">Drag & drop DICOM files or click to browse</p>
                        <input type="file" class="hidden">
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Processing Options</label>
                    <div class="space-y-4">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="ml-2 text-gray-700">Tumor Segmentation</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="ml-2 text-gray-700">3D Reconstruction</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-600">
                                <span class="ml-2 text-gray-700">Edema Detection</span>
                            </label>
                        </div>
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition">
                            Process Scan
                        </button>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sample Results</label>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="bg-gray-200 h-24 rounded"></div>
                        <div class="bg-gray-200 h-24 rounded"></div>
                        <div class="bg-gray-200 h-24 rounded"></div>
                        <div class="bg-gray-200 h-24 rounded"></div>
                    </div>
                    <div class="mt-3 text-center">
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View Full Report <i class="fas fa-arrow-right ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="p-4 border-b">
                    <h3 class="text-lg font-semibold text-blue-800">3D Tumor Visualization</h3>
                </div>
                <div class="p-6 h-96 bg-gray-100 flex items-center justify-center">
                    <div class="text-center">
                        <i class="fas fa-brain text-6xl text-blue-400 mb-4"></i>
                        <p class="text-gray-500">3D model will appear here after processing</p>
                    </div>
                </div>
                <div class="p-4 border-t flex justify-between items-center">
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-1"></i> Rotate with mouse, scroll to zoom
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-blue-100 text-blue-700 px-3 py-1 rounded text-sm">
                            <i class="fas fa-ruler mr-1"></i> Measure
                        </button>
                        <button class="bg-blue-100 text-blue-700 px-3 py-1 rounded text-sm">
                            <i class="fas fa-download mr-1"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Publications Section -->
        <section id="publications" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-blue-900 mb-8">Research Publications</h2>
            
            <div class="space-y-6 max-w-4xl mx-auto">
                <div class="neuro-card bg-white p-6">
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Automated Brain Tumor Segmentation in 3D MRI Using Deep Convolutional Neural Networks</h3>
                    <p class="text-gray-600 mb-3">Mohammed Yagoub Esmail, SUST-BME, 2024</p>
                    <p class="text-gray-700 mb-4">This paper presents our novel 3D U-Net architecture with attention gates for improved brain tumor segmentation, achieving state-of-the-art results on the BraTS challenge dataset.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">Deep Learning</span>
                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">Medical Imaging</span>
                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">3D Reconstruction</span>
                    </div>
                </div>
                
                <div class="neuro-card bg-white p-6">
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Explainable AI for Brain Tumor Diagnosis: Visualizing Model Decisions in Medical Imaging</h3>
                    <p class="text-gray-600 mb-3">Mohammed Yagoub Esmail, SUST-BME, 2025</p>
                    <p class="text-gray-700 mb-4">We introduce a framework combining Grad-CAM and uncertainty quantification to make deep learning models more interpretable for clinicians in brain tumor diagnosis.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">XAI</span>
                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">Clinical Decision Support</span>
                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">Interpretability</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="gradient-bg text-white rounded-xl p-8">
            <h2 class="text-3xl font-bold text-center mb-8">Contact Information</h2>
            
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <div>
                    <h3 class="text-xl font-semibold mb-4">Principal Investigator</h3>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-user mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium">Dr. Mohammed Yagoub Esmail</p>
                                <p class="text-blue-100 text-sm">Sudan University of Science and Technology</p>
                                <p class="text-blue-100 text-sm">Biomedical Engineering Department</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-envelope mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium">Email</p>
                                <p class="text-blue-100"><EMAIL></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-phone mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium">Phone</p>
                                <p class="text-blue-100">+249 912 867 327</p>
                                <p class="text-blue-100">+966 538 076 790</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-xl font-semibold mb-4">Send a Message</h3>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Name</label>
                            <input type="text" class="w-full bg-white/20 border border-white/30 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Email</label>
                            <input type="email" class="w-full bg-white/20 border border-white/30 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Message</label>
                            <textarea rows="3" class="w-full bg-white/20 border border-white/30 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-white"></textarea>
                        </div>
                        <button type="submit" class="bg-white text-blue-800 hover:bg-blue-100 px-6 py-2 rounded-md font-medium transition">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-blue-900 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-xl font-bold mb-2">Automated Brain Tumor Analysis</h3>
                    <p class="text-blue-200">Advanced Medical Imaging Research</p>
                </div>
                <div class="flex space-x-6">
                    <a href="#" class="text-blue-200 hover:text-white transition">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="#" class="text-blue-200 hover:text-white transition">
                        <i class="fab fa-linkedin text-xl"></i>
                    </a>
                    <a href="#" class="text-blue-200 hover:text-white transition">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                    <a href="#" class="text-blue-200 hover:text-white transition">
                        <i class="fab fa-researchgate text-xl"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-blue-800 mt-6 pt-6 text-center text-blue-300 text-sm">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail, SUST-BME. All rights reserved.</p>
                <p class="mt-1">This research is supported by the Biomedical Engineering Department, Sudan University of Science and Technology.</p>
            </div>
        </div>
    </footer>

    <script>
        // Simple script for demo purposes
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Mobile menu toggle would go here
            // const mobileMenuButton = document.querySelector('.md\\:hidden');
            // const mobileMenu = document.querySelector('.mobile-menu');
            // mobileMenuButton.addEventListener('click', () => {
            //     mobileMenu.classList.toggle('hidden');
            // });
        });
    </script>
</body>
</html>